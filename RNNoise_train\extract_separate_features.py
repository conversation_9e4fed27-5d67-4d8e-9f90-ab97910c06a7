#!/usr/bin/env python3
"""
分别提取清晰声音和噪声声音的特征
为每种类型创建独立的特征文件
"""

import os
import sys
import numpy as np
import glob
import argparse
import tqdm
from pathlib import Path

def load_pcm_file(pcm_file, sample_rate=48000):
    """加载PCM文件"""
    try:
        with open(pcm_file, 'rb') as f:
            data = np.frombuffer(f.read(), dtype=np.int16)
        # 转换为float32，范围[-1, 1]
        data = data.astype(np.float32) / 32768.0
        return data
    except Exception as e:
        print(f"加载PCM文件失败 {pcm_file}: {e}")
        return None

def extract_audio_features(audio_data, frame_size=480):
    """
    提取音频特征
    """
    num_frames = len(audio_data) // frame_size
    if num_frames < 5:  # 至少需要5帧
        return None
    
    # 初始化特征数组
    features = []
    
    for i in range(num_frames):
        start_idx = i * frame_size
        end_idx = start_idx + frame_size
        
        frame = audio_data[start_idx:end_idx]
        
        # 计算基本特征
        # 1. 能量特征 (32维)
        energy_features = np.zeros(32)
        for j in range(32):
            band_start = j * frame_size // 32
            band_end = (j + 1) * frame_size // 32
            energy_features[j] = np.mean(frame[band_start:band_end] ** 2)
        
        # 2. 频谱特征 (32维)
        fft_frame = np.fft.fft(frame)
        fft_magnitude = np.abs(fft_frame[:frame_size//2])
        spectral_features = np.zeros(32)
        for j in range(32):
            band_start = j * len(fft_magnitude) // 32
            band_end = (j + 1) * len(fft_magnitude) // 32
            spectral_features[j] = np.mean(fft_magnitude[band_start:band_end])
        
        # 3. 其他特征 (1维 - 基音特征)
        pitch_feature = np.array([np.std(frame)])
        
        # 组合特征 (总共65维)
        frame_features = np.concatenate([
            energy_features,      # 32维
            spectral_features,    # 32维  
            pitch_feature         # 1维
        ])
        
        # 计算增益 (32维) - 对于单独的音频，设为1
        gains = np.ones(32, dtype=np.float32)
        
        # 计算VAD (1维) - 基于能量
        frame_energy = np.mean(frame ** 2)
        vad = 1.0 if frame_energy > 0.001 else 0.0
        
        # 组合所有特征 (65 + 32 + 1 = 98维)
        combined_features = np.concatenate([
            frame_features,  # 65维特征
            gains,          # 32维增益
            [vad]           # 1维VAD
        ])
        
        features.append(combined_features)
    
    return np.array(features, dtype=np.float32)

def generate_features_for_type(pcm_files, output_file, audio_type, 
                              sequence_length=200, max_sequences=2000):
    """为特定类型的音频生成特征"""
    
    print(f"\n生成{audio_type}特征...")
    print(f"PCM文件数: {len(pcm_files)}")
    print(f"目标序列数: {max_sequences}")
    print(f"序列长度: {sequence_length}")
    
    all_features = []
    sequences_generated = 0
    
    # 创建输出目录
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with tqdm.tqdm(total=max_sequences, desc=f"生成{audio_type}特征序列") as pbar:
        for pcm_file in pcm_files:
            if sequences_generated >= max_sequences:
                break
                
            # 加载PCM数据
            audio_data = load_pcm_file(pcm_file)
            
            if audio_data is None:
                continue
            
            # 提取特征
            features = extract_audio_features(audio_data)
            
            if features is None:
                print(f"跳过文件 {pcm_file}: 特征提取失败")
                continue
            
            print(f"处理文件 {os.path.basename(pcm_file)}: 生成 {features.shape[0]} 帧特征")
            
            # 分割成序列
            num_frames = features.shape[0]
            
            # 如果帧数不足一个完整序列，跳过
            if num_frames < sequence_length:
                print(f"  帧数不足 ({num_frames} < {sequence_length})，跳过")
                continue
                
            num_sequences = num_frames // sequence_length
            print(f"  可生成 {num_sequences} 个序列")
            
            for seq_idx in range(num_sequences):
                if sequences_generated >= max_sequences:
                    break
                    
                start_frame = seq_idx * sequence_length
                end_frame = start_frame + sequence_length
                
                sequence_features = features[start_frame:end_frame]
                all_features.append(sequence_features)
                
                sequences_generated += 1
                pbar.update(1)
    
    if not all_features:
        print(f"没有生成任何{audio_type}特征序列")
        return False
    
    # 合并所有特征
    print(f"合并 {len(all_features)} 个{audio_type}序列...")
    combined_features = np.concatenate(all_features, axis=0)
    
    print(f"最终{audio_type}特征形状: {combined_features.shape}")
    print(f"预期形状: ({len(all_features) * sequence_length}, 98)")
    
    # 保存为.f32格式
    combined_features.astype(np.float32).tofile(output_file)
    
    file_size = os.path.getsize(output_file) / (1024 * 1024)
    print(f"{audio_type}特征文件已保存: {output_file}")
    print(f"文件大小: {file_size:.2f} MB")
    
    return True

def verify_features(features_file, audio_type, expected_sequence_length=200):
    """验证特征文件"""
    try:
        data = np.memmap(features_file, dtype='float32', mode='r')
        print(f"\n{audio_type}特征文件验证:")
        print(f"  总数据量: {data.shape[0]} 个float32值")
        
        # RNNoise特征格式: 每帧98维
        dim = 98
        
        total_frames = data.shape[0] // dim
        sequences = total_frames // expected_sequence_length
        
        print(f"  总帧数: {total_frames}")
        print(f"  序列数: {sequences}")
        print(f"  每序列长度: {expected_sequence_length}")
        print(f"  每帧维度: {dim}")
        
        if total_frames > 0:
            print(f"✓ {audio_type}特征文件格式正确，可用于训练")
            return True
        else:
            print(f"✗ {audio_type}特征文件格式错误")
            return False
            
    except Exception as e:
        print(f"验证{audio_type}特征文件失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='分别提取清晰和噪声音频特征')
    parser.add_argument('--clean-pcm-dir', default='clean_PCM_data',
                       help='清晰语音PCM目录')
    parser.add_argument('--noise-pcm-dir', default='noise_PCM_data',
                       help='噪声语音PCM目录')
    parser.add_argument('--output-dir', default='features',
                       help='特征输出目录')
    parser.add_argument('--max-sequences', type=int, default=2000,
                       help='每种类型的最大序列数')
    parser.add_argument('--sequence-length', type=int, default=200,
                       help='序列长度')
    
    args = parser.parse_args()
    
    print("=== 分别提取清晰和噪声音频特征 ===")
    print(f"清晰语音PCM目录: {args.clean_pcm_dir}")
    print(f"噪声语音PCM目录: {args.noise_pcm_dir}")
    print(f"输出目录: {args.output_dir}")
    print(f"每种类型最大序列数: {args.max_sequences}")
    
    # 获取PCM文件列表
    clean_pcm_files = sorted(glob.glob(os.path.join(args.clean_pcm_dir, "*.pcm")))
    noise_pcm_files = sorted(glob.glob(os.path.join(args.noise_pcm_dir, "*.pcm")))
    
    if not clean_pcm_files:
        print(f"没有找到清晰语音PCM文件: {args.clean_pcm_dir}")
        return
    
    if not noise_pcm_files:
        print(f"没有找到噪声语音PCM文件: {args.noise_pcm_dir}")
        return
    
    print(f"找到清晰语音PCM文件: {len(clean_pcm_files)}")
    print(f"找到噪声语音PCM文件: {len(noise_pcm_files)}")
    
    # 生成清晰语音特征
    clean_output = os.path.join(args.output_dir, "clean_features.f32")
    clean_success = generate_features_for_type(
        clean_pcm_files, clean_output, "清晰语音",
        args.sequence_length, args.max_sequences
    )
    
    # 生成噪声语音特征
    noise_output = os.path.join(args.output_dir, "noise_features.f32")
    noise_success = generate_features_for_type(
        noise_pcm_files, noise_output, "噪声语音",
        args.sequence_length, args.max_sequences
    )
    
    # 验证特征
    if clean_success:
        verify_features(clean_output, "清晰语音", args.sequence_length)
    
    if noise_success:
        verify_features(noise_output, "噪声语音", args.sequence_length)
    
    if clean_success and noise_success:
        print(f"\n✓ 特征提取完成！")
        print(f"清晰语音特征: {clean_output}")
        print(f"噪声语音特征: {noise_output}")
        print("现在有两个独立的特征文件可用于不同的训练目的")
    else:
        print(f"\n✗ 特征提取失败！")

if __name__ == "__main__":
    main()
