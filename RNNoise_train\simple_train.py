#!/usr/bin/env python3
"""
简化的RNNoise训练脚本
基于现有的train_rnnoise.py，适配自定义数据
"""

import os
import sys
import argparse
import torch
import numpy as np
from pathlib import Path

# 添加torch/rnnoise到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'torch', 'rnnoise'))

# 导入RNNoise相关模块
import rnnoise
import tqdm

# 从train_rnnoise.py复制必要的类和函数
class RNNoiseDataset(torch.utils.data.Dataset):
    def __init__(self, features_file, sequence_length=2000):
        self.sequence_length = sequence_length
        self.data = np.memmap(features_file, dtype='float32', mode='r')
        dim = 98
        self.nb_sequences = self.data.shape[0]//self.sequence_length//dim
        self.data = self.data[:self.nb_sequences*self.sequence_length*dim]
        self.data = np.reshape(self.data, (self.nb_sequences, self.sequence_length, dim))

    def __len__(self):
        return self.nb_sequences

    def __getitem__(self, index):
        return self.data[index, :, :65].copy(), self.data[index, :, 65:-1].copy(), self.data[index, :, -1:].copy()

def mask(g):
    return torch.clamp(g+1, max=1)

def train_custom_model(features_file, model_dir, epochs=50, batch_size=32, lr=1e-3):
    """训练自定义RNNoise模型"""
    
    # 创建模型目录
    os.makedirs(model_dir, exist_ok=True)
    
    # 设备选择
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 检查特征文件
    if not os.path.exists(features_file):
        print(f"错误: 特征文件不存在: {features_file}")
        return None
    
    print(f"特征文件大小: {os.path.getsize(features_file) / (1024*1024):.2f} MB")
    
    # 创建数据集
    try:
        # 使用较小的序列长度以适应较少的数据
        sequence_length = 500  # 减小序列长度
        dataset = RNNoiseDataset(features_file, sequence_length=sequence_length)
        
        if len(dataset) == 0:
            print("错误: 数据集为空")
            return None
            
        print(f"数据集大小: {len(dataset)} 个序列")
        
        # 创建数据加载器
        dataloader = torch.utils.data.DataLoader(
            dataset, 
            batch_size=min(batch_size, len(dataset)), 
            shuffle=True, 
            drop_last=True, 
            num_workers=0  # Windows兼容性
        )
        
    except Exception as e:
        print(f"创建数据集失败: {e}")
        return None
    
    # 创建模型 - 使用较小的参数
    model_kwargs = {'cond_size': 64, 'gru_size': 128}  # 减小模型大小
    model = rnnoise.RNNoise(**model_kwargs)
    model.to(device)
    
    # 优化器
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr, 
                                betas=[0.8, 0.98], eps=1e-8)
    
    # 学习率调度器
    scheduler = torch.optim.lr_scheduler.LambdaLR(
        optimizer=optimizer, 
        lr_lambda=lambda x: 1 / (1 + 5e-5 * x)
    )
    
    # 训练循环
    best_loss = float('inf')
    gamma = 0.25
    
    print(f"开始训练，共 {epochs} 轮")
    
    for epoch in range(1, epochs + 1):
        model.train()
        running_gain_loss = 0
        running_vad_loss = 0
        running_loss = 0
        
        print(f"训练轮次 {epoch}/{epochs}")
        
        states = None
        with tqdm.tqdm(dataloader, unit='batch') as tepoch:
            for i, (features, gain, vad) in enumerate(tepoch):
                optimizer.zero_grad()
                
                # 数据移到设备
                features = features.to(device)
                gain = gain.to(device)
                vad = vad.to(device)
                
                # 前向传播
                pred_gain, pred_vad, states = model(features, states=states)
                states = [state.detach() for state in states]
                
                # 调整目标数据维度
                gain = gain[:, 3:-1, :]
                vad = vad[:, 3:-1, :]
                
                # 计算目标增益
                target_gain = torch.clamp(gain, min=0)
                target_gain = target_gain * (torch.tanh(8 * target_gain) ** 2)
                
                # 增益损失
                e = pred_gain ** gamma - target_gain ** gamma
                gain_loss = torch.mean((1 + 5. * vad) * mask(gain) * (e ** 2))
                
                # VAD损失
                vad_loss = torch.mean(
                    torch.abs(2 * vad - 1) * (
                        -vad * torch.log(.01 + pred_vad) - 
                        (1 - vad) * torch.log(1.01 - pred_vad)
                    )
                )
                
                # 总损失
                loss = gain_loss + .001 * vad_loss
                
                # 反向传播
                loss.backward()
                optimizer.step()
                scheduler.step()
                
                # 统计
                running_gain_loss += gain_loss.detach().cpu().item()
                running_vad_loss += vad_loss.detach().cpu().item()
                running_loss += loss.detach().cpu().item()
                
                tepoch.set_postfix(
                    loss=f"{running_loss/(i+1):8.5f}",
                    gain_loss=f"{running_gain_loss/(i+1):8.5f}",
                    vad_loss=f"{running_vad_loss/(i+1):8.5f}",
                )
        
        # 保存检查点
        avg_loss = running_loss / len(dataloader)
        checkpoint = {
            'model_args': (),
            'model_kwargs': model_kwargs,
            'state_dict': model.state_dict(),
            'loss': avg_loss,
            'epoch': epoch
        }
        
        checkpoint_path = os.path.join(model_dir, f'rnnoise_custom_{epoch}.pth')
        torch.save(checkpoint, checkpoint_path)
        
        # 保存最佳模型
        if avg_loss < best_loss:
            best_loss = avg_loss
            best_model_path = os.path.join(model_dir, 'best_model.pth')
            torch.save(checkpoint, best_model_path)
            print(f"保存最佳模型，损失: {best_loss:.5f}")
    
    print("训练完成！")
    return model

def main():
    parser = argparse.ArgumentParser(description='训练自定义RNNoise模型')
    parser.add_argument('--features', default='features/training_features.f32',
                       help='特征文件路径')
    parser.add_argument('--model-dir', default='model',
                       help='模型保存目录')
    parser.add_argument('--epochs', type=int, default=50,
                       help='训练轮次')
    parser.add_argument('--batch-size', type=int, default=16,
                       help='批次大小')
    parser.add_argument('--lr', type=float, default=1e-3,
                       help='学习率')
    
    args = parser.parse_args()
    
    print("=== 自定义RNNoise模型训练 ===")
    print(f"特征文件: {args.features}")
    print(f"模型目录: {args.model_dir}")
    print(f"训练轮次: {args.epochs}")
    print(f"批次大小: {args.batch_size}")
    print(f"学习率: {args.lr}")
    
    # 检查特征文件
    if not os.path.exists(args.features):
        print(f"错误: 特征文件不存在: {args.features}")
        print("请先运行 prepare_training_data.py 生成特征文件")
        return
    
    # 开始训练
    model = train_custom_model(
        args.features, 
        args.model_dir, 
        args.epochs, 
        args.batch_size, 
        args.lr
    )
    
    if model is not None:
        print(f"\n训练成功完成！")
        print(f"模型保存在: {args.model_dir}")
        print("文件列表:")
        for file in sorted(os.listdir(args.model_dir)):
            if file.endswith('.pth'):
                file_path = os.path.join(args.model_dir, file)
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                print(f"  {file} ({size_mb:.2f} MB)")
    else:
        print("训练失败！")

if __name__ == "__main__":
    main()
