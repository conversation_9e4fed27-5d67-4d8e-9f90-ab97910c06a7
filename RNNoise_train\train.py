#!/usr/bin/env python3
"""
RNNoise训练脚本 - 使用原始RNNoise代码训练
使用方法: python train.py --epochs 500
"""

import os
import sys
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import tqdm
import argparse

# 添加torch/rnnoise到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'torch', 'rnnoise'))
import rnnoise

class RNNoiseDataset(Dataset):
    """RNNoise数据集"""
    def __init__(self, features_file, sequence_length=200):
        self.sequence_length = sequence_length

        # 加载特征数据
        self.data = np.memmap(features_file, dtype='float32', mode='r')
        dim = 98  # 特征维度

        # 计算序列数量
        total_frames = self.data.shape[0] // dim
        self.nb_sequences = total_frames // sequence_length

        if self.nb_sequences == 0:
            raise ValueError(f"数据不足以创建序列。总帧数: {total_frames}, 序列长度: {sequence_length}")

        # 重塑数据
        valid_data_size = self.nb_sequences * sequence_length * dim
        self.data = self.data[:valid_data_size]
        self.data = np.reshape(self.data, (self.nb_sequences, sequence_length, dim))

        print(f"数据集: {self.nb_sequences} 个序列, 每序列 {sequence_length} 帧")

    def __len__(self):
        return self.nb_sequences

    def __getitem__(self, index):
        # 返回特征(65维)、增益(32维)、VAD(1维)
        return (
            self.data[index, :, :65].copy(),    # 特征
            self.data[index, :, 65:-1].copy(),  # 增益
            self.data[index, :, -1:].copy()     # VAD
        )

def mask(g):
    """增益掩码函数"""
    return torch.clamp(g + 1, max=1)

def train_model(features_file, output_dir, epochs=200, batch_size=32, lr=1e-3, sequence_length=200):
    """训练RNNoise模型"""

    # 设备选择
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 创建数据集
    try:
        dataset = RNNoiseDataset(features_file, sequence_length)
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True,
                              drop_last=True, num_workers=0)
    except Exception as e:
        print(f"创建数据集失败: {e}")
        return False

    # 创建模型
    model = rnnoise.RNNoise(cond_size=128, gru_size=384)
    model.to(device)

    print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")

    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=lr, betas=[0.8, 0.98], eps=1e-8)
    scheduler = optim.lr_scheduler.LambdaLR(optimizer, lr_lambda=lambda x: 1 / (1 + 5e-5 * x))

    # 创建输出目录
    checkpoint_dir = os.path.join(output_dir, 'checkpoints')
    os.makedirs(checkpoint_dir, exist_ok=True)

    # 训练循环
    gamma = 0.25
    best_loss = float('inf')

    for epoch in range(1, epochs + 1):
        model.train()
        running_loss = 0.0
        running_gain_loss = 0.0
        running_vad_loss = 0.0

        print(f"训练轮次 {epoch}/{epochs}")

        states = None
        with tqdm.tqdm(dataloader, unit='batch') as tepoch:
            for i, (features, gain, vad) in enumerate(tepoch):
                optimizer.zero_grad()

                # 数据移到设备
                features = features.to(device)
                gain = gain.to(device)
                vad = vad.to(device)

                # 前向传播
                pred_gain, pred_vad, states = model(features, states=states)
                states = [state.detach() for state in states] if states else None

                # 调整目标数据维度 (去掉前3帧和最后1帧)
                if gain.shape[1] > 4:  # 确保有足够的帧
                    gain = gain[:, 3:-1, :]
                    vad = vad[:, 3:-1, :]

                    # 计算目标增益
                    target_gain = torch.clamp(gain, min=0)
                    target_gain = target_gain * (torch.tanh(8 * target_gain) ** 2)

                    # 增益损失
                    e = pred_gain ** gamma - target_gain ** gamma
                    gain_loss = torch.mean((1 + 5. * vad) * mask(gain) * (e ** 2))

                    # VAD损失
                    vad_loss = torch.mean(
                        torch.abs(2 * vad - 1) * (
                            -vad * torch.log(0.01 + pred_vad) -
                            (1 - vad) * torch.log(1.01 - pred_vad)
                        )
                    )

                    # 总损失
                    loss = gain_loss + 0.001 * vad_loss

                    # 反向传播
                    loss.backward()
                    optimizer.step()
                    scheduler.step()

                    # 统计
                    running_loss += loss.item()
                    running_gain_loss += gain_loss.item()
                    running_vad_loss += vad_loss.item()

                    tepoch.set_postfix(
                        loss=f"{running_loss/(i+1):.5f}",
                        gain_loss=f"{running_gain_loss/(i+1):.5f}",
                        vad_loss=f"{running_vad_loss/(i+1):.5f}"
                    )

        # 保存检查点
        if len(dataloader) > 0:
            avg_loss = running_loss / len(dataloader)

            checkpoint = {
                'model_args': (),
                'model_kwargs': {'cond_size': 128, 'gru_size': 384},
                'state_dict': model.state_dict(),
                'loss': avg_loss,
                'epoch': epoch
            }

            checkpoint_path = os.path.join(checkpoint_dir, f'rnnoise_{epoch}.pth')
            torch.save(checkpoint, checkpoint_path)

            if avg_loss < best_loss:
                best_loss = avg_loss
                best_path = os.path.join(checkpoint_dir, 'rnnoise_best.pth')
                torch.save(checkpoint, best_path)
                print(f"保存最佳模型，损失: {best_loss:.5f}")

    print("训练完成！")
    return True

def main():
    parser = argparse.ArgumentParser(description='RNNoise模型训练')
    parser.add_argument('--epochs', type=int, default=200,
                       help='训练轮次 (默认: 200)')
    parser.add_argument('--batch-size', type=int, default=32,
                       help='批次大小 (默认: 32)')
    parser.add_argument('--lr', type=float, default=1e-3,
                       help='学习率 (默认: 1e-3)')
    parser.add_argument('--sequence-length', type=int, default=200,
                       help='序列长度 (默认: 200)')

    args = parser.parse_args()

    # 特征文件路径
    features_file = "features/training_features.f32"
    output_dir = "model_output"

    print("=== RNNoise模型训练 ===")
    print(f"特征文件: {features_file}")
    print(f"输出目录: {output_dir}")
    print(f"训练轮次: {args.epochs}")
    print(f"批次大小: {args.batch_size}")
    print(f"学习率: {args.lr}")
    print(f"序列长度: {args.sequence_length}")

    # 检查特征文件
    if not os.path.exists(features_file):
        print(f"错误: 特征文件不存在: {features_file}")
        return

    file_size = os.path.getsize(features_file) / (1024 * 1024)
    print(f"特征文件大小: {file_size:.2f} MB")

    # 开始训练
    success = train_model(features_file, output_dir, args.epochs,
                         args.batch_size, args.lr, args.sequence_length)

    if success:
        # 显示生成的模型文件
        checkpoint_dir = os.path.join(output_dir, 'checkpoints')
        if os.path.exists(checkpoint_dir):
            model_files = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pth')]
            if model_files:
                print(f"\n生成的模型文件 ({len(model_files)} 个):")
                for file in sorted(model_files):
                    file_path = os.path.join(checkpoint_dir, file)
                    size_mb = os.path.getsize(file_path) / (1024 * 1024)
                    print(f"  {file} ({size_mb:.2f} MB)")

if __name__ == "__main__":
    main()
