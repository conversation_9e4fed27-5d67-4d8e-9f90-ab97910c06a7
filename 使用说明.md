# RNNoise 自定义模型训练完成！

## 🎉 训练成功

您的RNNoise自定义模型已经成功训练完成！以下是详细的使用说明。

## 📁 生成的文件

### 模型文件 (model/ 目录)
- `best_model.pth` - 最佳模型 (推荐使用)
- `rnnoise_custom_1.pth` - 第1轮训练结果
- `rnnoise_custom_2.pth` - 第2轮训练结果
- `rnnoise_custom_3.pth` - 第3轮训练结果
- `rnnoise_custom_4.pth` - 第4轮训练结果
- `rnnoise_custom_5.pth` - 第5轮训练结果

### 训练脚本
- `prepare_training_data.py` - 数据准备脚本
- `simple_train.py` - 训练脚本
- `run_training.py` - 完整训练流程
- `test_model.py` - 模型测试脚本

### 特征文件
- `features/training_features.f32` - 训练特征数据

## 🚀 快速使用

### 1. 测试模型
```bash
# 测试最佳模型
python test_model.py

# 比较所有模型性能
python test_model.py --compare
```

### 2. 继续训练
```bash
# 使用更多数据继续训练
python run_training.py --max-files 50 --epochs 100

# 或者直接训练
python simple_train.py --epochs 50 --batch-size 16
```

### 3. 转换为C代码 (可选)
```bash
# 转换最佳模型为C代码，用于集成到C项目
python torch/rnnoise/dump_rnnoise_weights.py --quantize model/best_model.pth rnnoise_c
```

## 📊 训练结果

根据训练日志，您的模型训练效果：

- **总训练轮次**: 5轮
- **最佳损失**: 0.08317 (第5轮)
- **模型大小**: 约1.35 MB
- **参数数量**: 351,393个参数

### 损失变化趋势
1. 第1轮: 0.10861
2. 第2轮: 0.10023
3. 第3轮: 0.09261
4. 第4轮: 0.08805
5. 第5轮: 0.08317 ⭐ (最佳)

## 🔧 模型使用示例

### Python中使用模型
```python
import torch
import sys
import os

# 添加路径
sys.path.append('torch/rnnoise')
import rnnoise

# 加载模型
checkpoint = torch.load('model/best_model.pth', map_location='cpu')
model = rnnoise.RNNoise(**checkpoint['model_kwargs'])
model.load_state_dict(checkpoint['state_dict'])
model.eval()

# 使用模型进行推理
with torch.no_grad():
    # input_features: (batch_size, sequence_length, 65)
    pred_gain, pred_vad, states = model(input_features)
```

## 📈 进一步优化建议

### 1. 增加训练数据
```bash
# 使用更多WAV文件
python run_training.py --max-files 100 --epochs 200
```

### 2. 调整模型参数
- 增加训练轮次 (`--epochs`)
- 调整批次大小 (`--batch-size`)
- 修改学习率 (`--lr`)

### 3. 使用真实特征提取
如果您编译了`dump_features`程序，可以使用真实的音频特征：
```bash
python run_training.py --max-files 50 --epochs 100
# (不使用 --simple 参数)
```

## 🛠️ 故障排除

### 常见问题

1. **模型加载失败**
   - 确保使用正确的Python环境
   - 检查torch/rnnoise路径是否正确

2. **内存不足**
   - 减少批次大小: `--batch-size 4`
   - 减少处理文件数: `--max-files 10`

3. **训练速度慢**
   - 如果有GPU，确保PyTorch支持CUDA
   - 减少序列长度或批次大小

## 📝 技术细节

### 模型架构
- **输入维度**: 65 (音频特征)
- **输出维度**: 32 (增益) + 1 (VAD)
- **网络结构**: 卷积层 + 3层GRU + 全连接层
- **参数配置**: cond_size=128, gru_size=256

### 训练配置
- **序列长度**: 500帧
- **批次大小**: 2-16 (可调)
- **学习率**: 0.001
- **优化器**: AdamW
- **损失函数**: 增益损失 + VAD损失

## 🎯 下一步

1. **测试音频效果**: 使用训练好的模型处理实际音频
2. **集成到应用**: 将模型集成到您的音频处理应用中
3. **持续优化**: 根据实际效果调整训练参数
4. **部署使用**: 转换为C代码用于生产环境

## 📞 支持

如果您在使用过程中遇到问题：

1. 查看训练日志了解详细信息
2. 使用`test_model.py`验证模型状态
3. 调整训练参数重新训练
4. 检查输入数据格式和质量

---

**恭喜您成功训练了自定义的RNNoise模型！** 🎊

现在您可以使用这个模型进行音频降噪处理了。
