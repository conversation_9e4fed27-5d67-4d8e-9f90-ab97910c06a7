# RNNoise 数据准备完成报告

## ✅ 任务完成状态

### 1. WAV转PCM转换 ✅
- **清晰语音**: 100个WAV文件 → 100个PCM文件
- **噪声语音**: 100个WAV文件 → 100个PCM文件
- **格式**: 48kHz, 16位, 单声道
- **位置**: 
  - `RNNoise_train/clean_PCM_data/` (100个文件)
  - `RNNoise_train/noise_PCM_data/` (100个文件)

### 2. 特征提取 ✅
- **特征文件**: `RNNoise_train/features/training_features.f32`
- **文件大小**: 7.84 MB
- **数据格式**: 
  - 总帧数: 20,000帧
  - 每帧维度: 98维 (65特征 + 32增益 + 1VAD)
  - 序列数: 100个序列
  - 序列长度: 200帧/序列

## 📁 文件结构

```
RNNoise_train/
├── clean_PCM_data/          # 清晰语音PCM文件 (100个文件)
│   ├── 000_001.pcm
│   ├── 000_002.pcm
│   └── ... (共100个)
├── noise_PCM_data/          # 噪声语音PCM文件 (100个文件)  
│   ├── 000_001_mixed.pcm
│   ├── 000_002_mixed.pcm
│   └── ... (共100个)
├── features/                # 训练特征文件
│   └── training_features.f32 (7.84 MB)
├── model_output/            # 模型输出目录 (空)
├── prepare_pcm_data.py      # 数据准备脚本
├── extract_features_python.py # Python特征提取脚本
├── train_with_original.py   # 原始训练包装脚本
├── run_full_training.py     # 完整训练流程
└── README.md               # 使用说明
```

## 📊 数据统计

### PCM文件
- **清晰语音PCM**: 100个文件，每个288KB (3秒音频)
- **噪声语音PCM**: 100个文件，每个288KB (3秒音频)
- **总音频时长**: 600秒 (10分钟)
- **采样率**: 48kHz
- **位深**: 16位
- **声道**: 单声道

### 特征数据
- **特征文件**: training_features.f32
- **数据量**: 1,960,000个float32值
- **总帧数**: 20,000帧
- **特征维度**: 98维/帧
  - 65维: 音频特征 (能量+频谱+基音)
  - 32维: 增益目标
  - 1维: VAD (语音活动检测)
- **序列配置**: 100个序列 × 200帧/序列

## 🔧 处理过程

### 步骤1: WAV到PCM转换
```bash
# 使用ffmpeg转换
ffmpeg -i input.wav -f s16le -ar 48000 -ac 1 output.pcm
```

### 步骤2: 特征提取
- 使用Python实现的简化特征提取
- 每帧480个样本 (10ms @ 48kHz)
- 提取能量、频谱、基音特征
- 计算增益目标和VAD标签

## 🎯 训练准备

### 数据已就绪
- ✅ PCM数据: 200个文件 (100对)
- ✅ 特征文件: 7.84MB训练数据
- ✅ 格式兼容: 符合RNNoise训练要求

### 可用于训练
特征文件 `features/training_features.f32` 现在可以直接用于:

1. **原始RNNoise训练**:
   ```bash
   python ../torch/rnnoise/train_rnnoise.py features/training_features.f32 model_output
   ```

2. **使用包装脚本**:
   ```bash
   python train_with_original.py --features features/training_features.f32
   ```

3. **完整训练流程**:
   ```bash
   python run_full_training.py --skip-data-prep
   ```

## 📈 建议的训练参数

### 基础训练
```bash
python train_with_original.py \
  --features features/training_features.f32 \
  --epochs 100 \
  --batch-size 32 \
  --sequence-length 200
```

### 高级训练
```bash
python train_with_original.py \
  --features features/training_features.f32 \
  --epochs 200 \
  --batch-size 64 \
  --lr 1e-3 \
  --cond-size 128 \
  --gru-size 384
```

## ⚠️ 注意事项

1. **序列长度**: 当前特征使用200帧序列，训练时需要匹配
2. **数据量**: 100个序列相对较少，建议增加更多音频数据
3. **特征质量**: 使用简化特征提取，实际效果可能不如原始dump_features
4. **内存使用**: 7.84MB特征文件，训练时内存需求适中

## 🚀 下一步

1. **开始训练**: 使用准备好的特征文件训练模型
2. **增加数据**: 如需更好效果，可添加更多WAV文件
3. **调整参数**: 根据训练效果调整网络参数
4. **模型评估**: 训练完成后测试模型性能

---

**数据准备完成时间**: 2025年7月30日 12:26
**处理文件数**: 200个PCM文件
**生成特征**: 20,000帧 × 98维
**状态**: ✅ 就绪，可开始训练
