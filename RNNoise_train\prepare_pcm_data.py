#!/usr/bin/env python3
"""
完整的RNNoise数据准备脚本
1. 将WAV文件转换为PCM格式
2. 使用原始dump_features程序提取特征
3. 准备用于原始train_rnnoise.py的数据
"""

import os
import sys
import subprocess
import glob
import argparse
import numpy as np
import tqdm
from pathlib import Path

class PCMDataPreparator:
    """PCM数据准备类"""
    
    def __init__(self, ffmpeg_path="../env/ffmpeg.exe"):
        self.ffmpeg_path = ffmpeg_path
        
    def wav_to_pcm(self, wav_file, pcm_file, sample_rate=48000):
        """将WAV文件转换为16位PCM格式"""
        cmd = [
            self.ffmpeg_path,
            '-i', wav_file,
            '-f', 's16le',  # 16位小端格式
            '-ar', str(sample_rate),  # 采样率
            '-ac', '1',  # 单声道
            '-y',  # 覆盖输出文件
            pcm_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"转换失败 {wav_file}: {e.stderr}")
            return False
    
    def convert_wav_directory(self, wav_dir, pcm_dir, sample_rate=48000, max_files=None):
        """批量转换WAV目录到PCM目录"""
        print(f"\n转换 {wav_dir} -> {pcm_dir}")
        
        # 创建PCM输出目录
        os.makedirs(pcm_dir, exist_ok=True)
        
        # 获取WAV文件列表
        wav_files = sorted(glob.glob(os.path.join(wav_dir, "*.wav")))
        if max_files:
            wav_files = wav_files[:max_files]
            
        print(f"找到 {len(wav_files)} 个WAV文件")
        
        success_files = []
        failed_files = []
        
        for wav_file in tqdm.tqdm(wav_files, desc="转换WAV到PCM"):
            filename = os.path.splitext(os.path.basename(wav_file))[0]
            pcm_file = os.path.join(pcm_dir, f"{filename}.pcm")
            
            if self.wav_to_pcm(wav_file, pcm_file, sample_rate):
                success_files.append(pcm_file)
            else:
                failed_files.append(wav_file)
        
        print(f"成功转换: {len(success_files)}/{len(wav_files)} 个文件")
        if failed_files:
            print(f"转换失败的文件: {failed_files}")
        
        return success_files
    
    def create_file_list(self, pcm_files, list_file):
        """创建PCM文件列表"""
        with open(list_file, 'w') as f:
            for pcm_file in pcm_files:
                # 使用绝对路径
                abs_path = os.path.abspath(pcm_file)
                f.write(f"{abs_path}\n")
        print(f"创建文件列表: {list_file} ({len(pcm_files)} 个文件)")
        return list_file
    
    def check_dump_features(self):
        """检查dump_features程序"""
        possible_paths = [
            "../src/dump_features.exe",
            "../src/dump_features",
            "src/dump_features.exe", 
            "src/dump_features"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                print(f"找到dump_features程序: {path}")
                return path
        
        print("未找到dump_features程序")
        print("请编译dump_features程序:")
        print("cd ../src")
        print("gcc -o dump_features dump_features.c denoise.c kiss_fft.c celt_lpc.c pitch.c nnet.c rnn.c parse_lpcnet_weights.c -lm -O2")
        return None
    
    def extract_features(self, clean_pcm_files, noise_pcm_files, output_file, count=2000):
        """使用dump_features提取特征"""
        
        # 检查dump_features程序
        dump_features_exe = self.check_dump_features()
        if not dump_features_exe:
            return False
        
        # 确保有足够的文件
        min_files = min(len(clean_pcm_files), len(noise_pcm_files))
        if min_files == 0:
            print("错误: 没有PCM文件可用于特征提取")
            return False
        
        print(f"\n使用 {min_files} 对PCM文件进行特征提取")
        print(f"生成 {count} 个训练序列")
        
        # 创建临时文件列表
        clean_list = "temp_clean_list.txt"
        noise_list = "temp_noise_list.txt" 
        empty_list = "temp_empty_list.txt"
        
        try:
            # 创建文件列表
            self.create_file_list(clean_pcm_files, clean_list)
            self.create_file_list(noise_pcm_files, noise_list)
            
            # 创建空的前景噪声列表
            with open(empty_list, 'w') as f:
                f.write("")
            
            # 创建输出目录
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            # 构建dump_features命令
            cmd = [
                dump_features_exe,
                clean_list,
                noise_list, 
                empty_list,
                output_file,
                str(count)
            ]
            
            print(f"运行命令: {' '.join(cmd)}")
            
            # 运行dump_features
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            print("特征提取完成！")
            if result.stdout:
                print(f"输出: {result.stdout}")
            
            # 验证输出文件
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"特征文件大小: {file_size / (1024*1024):.2f} MB")
                
                if file_size > 0:
                    print("✓ 特征提取成功！")
                    return True
                else:
                    print("✗ 特征文件为空")
                    return False
            else:
                print("✗ 特征文件未生成")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"特征提取失败: {e.stderr}")
            if e.stdout:
                print(f"标准输出: {e.stdout}")
            return False
        finally:
            # 清理临时文件
            for temp_file in [clean_list, noise_list, empty_list]:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
    
    def verify_features(self, features_file):
        """验证特征文件"""
        try:
            data = np.memmap(features_file, dtype='float32', mode='r')
            print(f"\n特征文件验证:")
            print(f"  总数据量: {data.shape[0]} 个float32值")
            
            # RNNoise特征格式: 每帧98维 (65特征 + 32增益 + 1VAD)
            dim = 98
            sequence_length = 2000
            
            total_frames = data.shape[0] // dim
            sequences = total_frames // sequence_length
            
            print(f"  总帧数: {total_frames}")
            print(f"  序列数: {sequences}")
            print(f"  每序列长度: {sequence_length}")
            print(f"  每帧维度: {dim}")
            
            if sequences > 0:
                print("✓ 特征文件格式正确，可用于训练")
                return True
            else:
                print("✗ 特征文件格式错误")
                return False
                
        except Exception as e:
            print(f"验证特征文件失败: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='准备RNNoise训练的PCM数据')
    parser.add_argument('--clean-wav-dir', default='../clean_voice',
                       help='清晰语音WAV文件目录')
    parser.add_argument('--noise-wav-dir', default='../noise_voice', 
                       help='噪声语音WAV文件目录')
    parser.add_argument('--clean-pcm-dir', default='clean_PCM_data',
                       help='清晰语音PCM输出目录')
    parser.add_argument('--noise-pcm-dir', default='noise_PCM_data',
                       help='噪声语音PCM输出目录')
    parser.add_argument('--features-file', default='features/training_features.f32',
                       help='特征文件输出路径')
    parser.add_argument('--sample-rate', type=int, default=48000,
                       help='PCM采样率')
    parser.add_argument('--max-files', type=int, default=100,
                       help='最大处理文件数')
    parser.add_argument('--count', type=int, default=2000,
                       help='生成的训练序列数量')
    parser.add_argument('--skip-conversion', action='store_true',
                       help='跳过WAV到PCM转换')
    
    args = parser.parse_args()
    
    print("=== RNNoise PCM数据准备 ===")
    print(f"清晰语音WAV目录: {args.clean_wav_dir}")
    print(f"噪声语音WAV目录: {args.noise_wav_dir}")
    print(f"清晰语音PCM目录: {args.clean_pcm_dir}")
    print(f"噪声语音PCM目录: {args.noise_pcm_dir}")
    print(f"特征文件: {args.features_file}")
    print(f"训练序列数量: {args.count}")
    
    preparator = PCMDataPreparator()
    
    # 步骤1: WAV转PCM
    if not args.skip_conversion:
        print("\n=== 步骤1: WAV转PCM ===")
        
        clean_pcm_files = preparator.convert_wav_directory(
            args.clean_wav_dir, args.clean_pcm_dir, 
            args.sample_rate, args.max_files)
        
        noise_pcm_files = preparator.convert_wav_directory(
            args.noise_wav_dir, args.noise_pcm_dir,
            args.sample_rate, args.max_files)
    else:
        print("\n跳过WAV转PCM，使用现有PCM文件")
        clean_pcm_files = glob.glob(os.path.join(args.clean_pcm_dir, "*.pcm"))
        noise_pcm_files = glob.glob(os.path.join(args.noise_pcm_dir, "*.pcm"))
    
    if not clean_pcm_files or not noise_pcm_files:
        print("错误: 没有可用的PCM文件")
        return
    
    print(f"可用的清晰语音PCM文件: {len(clean_pcm_files)}")
    print(f"可用的噪声语音PCM文件: {len(noise_pcm_files)}")
    
    # 步骤2: 特征提取
    print("\n=== 步骤2: 特征提取 ===")
    
    success = preparator.extract_features(
        clean_pcm_files, noise_pcm_files, 
        args.features_file, args.count)
    
    if success:
        # 步骤3: 验证特征
        print("\n=== 步骤3: 验证特征 ===")
        preparator.verify_features(args.features_file)
        
        print(f"\n✓ 数据准备完成！")
        print(f"PCM数据保存在:")
        print(f"  清晰语音: {args.clean_pcm_dir}")
        print(f"  噪声语音: {args.noise_pcm_dir}")
        print(f"特征文件: {args.features_file}")
        print(f"\n现在可以使用原始train_rnnoise.py进行训练:")
        print(f"python ../torch/rnnoise/train_rnnoise.py {args.features_file} model_output")
    else:
        print("\n✗ 数据准备失败！")

if __name__ == "__main__":
    main()
