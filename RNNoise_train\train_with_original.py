#!/usr/bin/env python3
"""
使用原始train_rnnoise.py进行训练的包装脚本
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def check_requirements():
    """检查训练所需的文件和环境"""
    print("检查训练环境...")
    
    # 检查原始训练脚本
    train_script = "../torch/rnnoise/train_rnnoise.py"
    if not os.path.exists(train_script):
        print(f"✗ 训练脚本不存在: {train_script}")
        return False
    else:
        print(f"✓ 找到训练脚本: {train_script}")
    
    # 检查rnnoise模块
    rnnoise_module = "../torch/rnnoise/rnnoise.py"
    if not os.path.exists(rnnoise_module):
        print(f"✗ RNNoise模块不存在: {rnnoise_module}")
        return False
    else:
        print(f"✓ 找到RNNoise模块: {rnnoise_module}")
    
    # 检查sparsification模块
    sparse_module = "../torch/sparsification"
    if not os.path.exists(sparse_module):
        print(f"✗ 稀疏化模块不存在: {sparse_module}")
        return False
    else:
        print(f"✓ 找到稀疏化模块: {sparse_module}")
    
    return True

def run_original_training(features_file, output_dir, **kwargs):
    """运行原始的train_rnnoise.py"""
    
    # 检查特征文件
    if not os.path.exists(features_file):
        print(f"✗ 特征文件不存在: {features_file}")
        return False
    
    file_size = os.path.getsize(features_file) / (1024 * 1024)
    print(f"✓ 特征文件: {features_file} ({file_size:.2f} MB)")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 构建训练命令
    train_script = "../torch/rnnoise/train_rnnoise.py"
    cmd = [sys.executable, train_script, features_file, output_dir]
    
    # 添加可选参数
    if 'epochs' in kwargs and kwargs['epochs']:
        cmd.extend(['--epochs', str(kwargs['epochs'])])
    if 'batch_size' in kwargs and kwargs['batch_size']:
        cmd.extend(['--batch-size', str(kwargs['batch_size'])])
    if 'lr' in kwargs and kwargs['lr']:
        cmd.extend(['--lr', str(kwargs['lr'])])
    if 'cond_size' in kwargs and kwargs['cond_size']:
        cmd.extend(['--cond-size', str(kwargs['cond_size'])])
    if 'gru_size' in kwargs and kwargs['gru_size']:
        cmd.extend(['--gru-size', str(kwargs['gru_size'])])
    if 'sequence_length' in kwargs and kwargs['sequence_length']:
        cmd.extend(['--sequence-length', str(kwargs['sequence_length'])])
    if 'lr_decay' in kwargs and kwargs['lr_decay']:
        cmd.extend(['--lr-decay', str(kwargs['lr_decay'])])
    if 'gamma' in kwargs and kwargs['gamma']:
        cmd.extend(['--gamma', str(kwargs['gamma'])])
    if 'suffix' in kwargs and kwargs['suffix']:
        cmd.extend(['--suffix', kwargs['suffix']])
    if kwargs.get('sparse', False):
        cmd.append('--sparse')
    
    print(f"\n运行训练命令:")
    print(f"{' '.join(cmd)}")
    print(f"\n开始训练...")
    
    try:
        # 运行训练，实时显示输出
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, 
                                 stderr=subprocess.STDOUT, text=True, 
                                 universal_newlines=True)
        
        # 实时显示输出
        for line in process.stdout:
            print(line.rstrip())
        
        # 等待进程完成
        return_code = process.wait()
        
        if return_code == 0:
            print("\n✓ 训练完成！")
            return True
        else:
            print(f"\n✗ 训练失败，返回码: {return_code}")
            return False
            
    except Exception as e:
        print(f"\n✗ 训练过程中出错: {e}")
        return False

def list_model_files(output_dir):
    """列出生成的模型文件"""
    checkpoints_dir = os.path.join(output_dir, 'checkpoints')
    
    if not os.path.exists(checkpoints_dir):
        print(f"检查点目录不存在: {checkpoints_dir}")
        return
    
    model_files = [f for f in os.listdir(checkpoints_dir) if f.endswith('.pth')]
    
    if not model_files:
        print("没有找到模型文件")
        return
    
    print(f"\n生成的模型文件 ({len(model_files)} 个):")
    for file in sorted(model_files):
        file_path = os.path.join(checkpoints_dir, file)
        size_mb = os.path.getsize(file_path) / (1024 * 1024)
        print(f"  {file} ({size_mb:.2f} MB)")

def main():
    parser = argparse.ArgumentParser(description='使用原始RNNoise训练脚本')
    
    # 必需参数
    parser.add_argument('--features', default='features/training_features.f32',
                       help='特征文件路径')
    parser.add_argument('--output', default='model_output',
                       help='模型输出目录')
    
    # 训练参数 (与原始train_rnnoise.py保持一致)
    parser.add_argument('--epochs', type=int, default=200,
                       help='训练轮次 (默认: 200)')
    parser.add_argument('--batch-size', type=int, default=128,
                       help='批次大小 (默认: 128)')
    parser.add_argument('--lr', type=float, default=1e-3,
                       help='学习率 (默认: 1e-3)')
    parser.add_argument('--cond-size', type=int, default=128,
                       help='条件层大小 (默认: 128)')
    parser.add_argument('--gru-size', type=int, default=384,
                       help='GRU层大小 (默认: 384)')
    parser.add_argument('--sequence-length', type=int, default=2000,
                       help='序列长度 (默认: 2000)')
    parser.add_argument('--lr-decay', type=float, default=5e-5,
                       help='学习率衰减 (默认: 5e-5)')
    parser.add_argument('--gamma', type=float, default=0.25,
                       help='感知指数 (默认: 0.25)')
    parser.add_argument('--suffix', type=str, default='_custom',
                       help='模型名称后缀 (默认: _custom)')
    parser.add_argument('--sparse', action='store_true',
                       help='启用稀疏化训练')
    
    args = parser.parse_args()
    
    print("=== 使用原始RNNoise训练脚本 ===")
    
    # 检查环境
    if not check_requirements():
        print("\n环境检查失败，请确保所有必需文件存在")
        return
    
    # 显示训练配置
    print(f"\n训练配置:")
    print(f"  特征文件: {args.features}")
    print(f"  输出目录: {args.output}")
    print(f"  训练轮次: {args.epochs}")
    print(f"  批次大小: {args.batch_size}")
    print(f"  学习率: {args.lr}")
    print(f"  条件层大小: {args.cond_size}")
    print(f"  GRU大小: {args.gru_size}")
    print(f"  序列长度: {args.sequence_length}")
    print(f"  学习率衰减: {args.lr_decay}")
    print(f"  感知指数: {args.gamma}")
    print(f"  模型后缀: {args.suffix}")
    print(f"  稀疏化: {args.sparse}")
    
    # 检查特征文件
    if not os.path.exists(args.features):
        print(f"\n✗ 特征文件不存在: {args.features}")
        print("请先运行 prepare_pcm_data.py 生成特征文件")
        return
    
    # 运行训练
    success = run_original_training(
        args.features,
        args.output,
        epochs=args.epochs,
        batch_size=args.batch_size,
        lr=args.lr,
        cond_size=args.cond_size,
        gru_size=args.gru_size,
        sequence_length=args.sequence_length,
        lr_decay=args.lr_decay,
        gamma=args.gamma,
        suffix=args.suffix,
        sparse=args.sparse
    )
    
    if success:
        print(f"\n=== 训练成功完成 ===")
        print(f"模型保存在: {args.output}")
        
        # 列出生成的模型文件
        list_model_files(args.output)
        
        print(f"\n后续步骤:")
        print(f"1. 查看训练日志和损失曲线")
        print(f"2. 选择最佳模型进行测试")
        print(f"3. 使用dump_rnnoise_weights.py转换为C代码:")
        print(f"   python ../torch/rnnoise/dump_rnnoise_weights.py --quantize {args.output}/checkpoints/rnnoise{args.suffix}_<epoch>.pth output_c")
        
    else:
        print(f"\n=== 训练失败 ===")
        print("请检查:")
        print("1. 特征文件是否正确生成")
        print("2. 训练参数是否合适")
        print("3. 系统内存是否足够")
        print("4. PyTorch是否正确安装")

if __name__ == "__main__":
    main()
