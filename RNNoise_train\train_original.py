#!/usr/bin/env python3
"""
使用原始train_rnnoise.py进行训练的包装脚本
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_original_training(features_file, output_dir, **kwargs):
    """运行原始的train_rnnoise.py"""
    
    # 检查特征文件
    if not os.path.exists(features_file):
        print(f"错误: 特征文件不存在: {features_file}")
        return False
    
    # 检查原始训练脚本
    train_script = "../torch/rnnoise/train_rnnoise.py"
    if not os.path.exists(train_script):
        print(f"错误: 训练脚本不存在: {train_script}")
        return False
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 构建训练命令
    cmd = [
        sys.executable, train_script,
        features_file,
        output_dir
    ]
    
    # 添加可选参数
    if 'epochs' in kwargs:
        cmd.extend(['--epochs', str(kwargs['epochs'])])
    if 'batch_size' in kwargs:
        cmd.extend(['--batch-size', str(kwargs['batch_size'])])
    if 'lr' in kwargs:
        cmd.extend(['--lr', str(kwargs['lr'])])
    if 'cond_size' in kwargs:
        cmd.extend(['--cond-size', str(kwargs['cond_size'])])
    if 'gru_size' in kwargs:
        cmd.extend(['--gru-size', str(kwargs['gru_size'])])
    if 'sequence_length' in kwargs:
        cmd.extend(['--sequence-length', str(kwargs['sequence_length'])])
    if 'suffix' in kwargs:
        cmd.extend(['--suffix', kwargs['suffix']])
    if 'gamma' in kwargs:
        cmd.extend(['--gamma', str(kwargs['gamma'])])
    if kwargs.get('sparse', False):
        cmd.append('--sparse')
    
    print(f"运行训练命令: {' '.join(cmd)}")
    
    try:
        # 运行训练
        result = subprocess.run(cmd, check=True, text=True)
        print("训练完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"训练失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='使用原始RNNoise训练脚本')
    parser.add_argument('--features', default='features/training_features.f32',
                       help='特征文件路径')
    parser.add_argument('--output', default='model_output',
                       help='模型输出目录')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=200,
                       help='训练轮次')
    parser.add_argument('--batch-size', type=int, default=128,
                       help='批次大小')
    parser.add_argument('--lr', type=float, default=1e-3,
                       help='学习率')
    parser.add_argument('--cond-size', type=int, default=128,
                       help='条件层大小')
    parser.add_argument('--gru-size', type=int, default=384,
                       help='GRU层大小')
    parser.add_argument('--sequence-length', type=int, default=2000,
                       help='序列长度')
    parser.add_argument('--suffix', type=str, default='_custom',
                       help='模型名称后缀')
    parser.add_argument('--gamma', type=float, default=0.25,
                       help='感知指数')
    parser.add_argument('--sparse', action='store_true',
                       help='启用稀疏化')
    
    args = parser.parse_args()
    
    print("=== 使用原始RNNoise训练脚本 ===")
    print(f"特征文件: {args.features}")
    print(f"输出目录: {args.output}")
    print(f"训练参数:")
    print(f"  轮次: {args.epochs}")
    print(f"  批次大小: {args.batch_size}")
    print(f"  学习率: {args.lr}")
    print(f"  条件层大小: {args.cond_size}")
    print(f"  GRU大小: {args.gru_size}")
    print(f"  序列长度: {args.sequence_length}")
    print(f"  稀疏化: {args.sparse}")
    
    # 检查特征文件
    if not os.path.exists(args.features):
        print(f"\n错误: 特征文件不存在: {args.features}")
        print("请先运行 prepare_data_full.py 生成特征文件")
        return
    
    # 显示特征文件信息
    file_size = os.path.getsize(args.features) / (1024 * 1024)
    print(f"特征文件大小: {file_size:.2f} MB")
    
    # 运行训练
    success = run_original_training(
        args.features,
        args.output,
        epochs=args.epochs,
        batch_size=args.batch_size,
        lr=args.lr,
        cond_size=args.cond_size,
        gru_size=args.gru_size,
        sequence_length=args.sequence_length,
        suffix=args.suffix,
        gamma=args.gamma,
        sparse=args.sparse
    )
    
    if success:
        print(f"\n✓ 训练成功完成！")
        print(f"模型保存在: {args.output}")
        
        # 列出生成的模型文件
        if os.path.exists(args.output):
            checkpoints_dir = os.path.join(args.output, 'checkpoints')
            if os.path.exists(checkpoints_dir):
                model_files = [f for f in os.listdir(checkpoints_dir) if f.endswith('.pth')]
                if model_files:
                    print("生成的模型文件:")
                    for file in sorted(model_files):
                        file_path = os.path.join(checkpoints_dir, file)
                        size_mb = os.path.getsize(file_path) / (1024 * 1024)
                        print(f"  {file} ({size_mb:.2f} MB)")
    else:
        print("\n✗ 训练失败！")

if __name__ == "__main__":
    main()
