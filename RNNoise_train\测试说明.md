# RNNoise 模型测试说明

## 📁 测试文件

**`test_denoise.py`** - 唯一的测试脚本

## 🚀 使用方法

### 基础用法
```bash
python test_denoise.py input.wav output.wav
```

### 指定模型
```bash
python test_denoise.py input.wav output.wav --model model_output/checkpoints/rnnoise_100.pth
```

### 使用GPU (如果可用)
```bash
python test_denoise.py input.wav output.wav --device cuda
```

## 📋 参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `input_wav` | 输入WAV文件路径 | 必需 |
| `output_wav` | 输出WAV文件路径 | 必需 |
| `--model` | 模型文件路径 | `model_output/checkpoints/rnnoise_best.pth` |
| `--device` | 计算设备 | `cpu` |

## 🔧 处理流程

1. **WAV转PCM**: 将输入WAV转换为48kHz单声道PCM
2. **特征提取**: 提取65维音频特征 (能量+频谱+基音)
3. **模型推理**: 使用训练好的RNNoise模型预测增益
4. **增益应用**: 将预测的增益应用到原始音频
5. **格式转换**: 将处理后的音频转换回WAV格式

## 📊 测试示例

### 成功测试结果
```
=== RNNoise音频降噪测试 ===
输入文件: ../noise_voice/000_001_mixed.wav
输出文件: test_output.wav
模型文件: model_output/checkpoints/rnnoise_best.pth
计算设备: cpu

加载模型: model_output/checkpoints/rnnoise_best.pth
模型加载成功，参数数量: 2,884,769

步骤1: WAV转PCM
步骤2: 加载音频数据
音频长度: 144,000 样本 (3.00秒)
步骤3: 提取特征
特征形状: (300, 65)
步骤4: 模型降噪
增益形状: (296, 32)
步骤5: 应用增益
步骤6: 保存降噪音频
步骤7: PCM转WAV

✓ 降噪完成！
输出文件: test_output.wav
输入文件大小: 281.3 KB
输出文件大小: 281.3 KB
```

## 🎯 支持的音频格式

### 输入格式
- **WAV文件**: 任意采样率、任意声道数
- **自动转换**: 脚本会自动转换为48kHz单声道

### 输出格式
- **WAV文件**: 48kHz, 16位, 单声道
- **文件大小**: 与输入文件相近

## ⚠️ 注意事项

1. **模型文件**: 确保模型文件存在且训练完成
2. **ffmpeg路径**: 确保 `../env/ffmpeg.exe` 可用
3. **音频长度**: 支持任意长度的音频文件
4. **内存使用**: 长音频文件可能需要更多内存

## 🔍 故障排除

### 常见错误

1. **"模型文件不存在"**
   ```bash
   # 检查模型文件
   dir model_output\checkpoints\*.pth
   
   # 使用其他模型
   python test_denoise.py input.wav output.wav --model model_output/checkpoints/rnnoise_200.pth
   ```

2. **"WAV转PCM失败"**
   ```bash
   # 检查ffmpeg
   ..\env\ffmpeg.exe -version
   
   # 检查输入文件
   dir input.wav
   ```

3. **"特征提取失败"**
   - 检查音频文件是否损坏
   - 确保音频文件不为空

## 📈 效果评估

### 主观评估
- 播放原始音频和降噪后音频
- 对比噪声抑制效果
- 评估语音清晰度

### 客观指标
- 文件大小对比
- 处理时间统计
- 频谱分析对比

## 🎉 快速测试

```bash
# 使用项目中的测试音频
python test_denoise.py ../noise_voice/000_001_mixed.wav denoised_output.wav

# 使用自己的音频文件
python test_denoise.py my_noisy_audio.wav my_clean_audio.wav

# 批量测试多个文件
for file in ../noise_voice/*.wav; do
    output="denoised_$(basename "$file")"
    python test_denoise.py "$file" "$output"
done
```

---

**简单总结**: 使用 `python test_denoise.py input.wav output.wav` 即可对WAV音频进行降噪处理
