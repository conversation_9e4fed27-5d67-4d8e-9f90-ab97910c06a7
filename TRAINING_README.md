# RNNoise 自定义模型训练指南

本指南将帮助您使用自己的WAV文件训练RNNoise模型。

## 文件结构

```
├── clean_voice/          # 清晰语音WAV文件目录
├── noise_voice/          # 噪声语音WAV文件目录
├── model/               # 训练后的模型保存目录（自动创建）
├── features/            # 特征文件目录（自动创建）
├── temp_clean_pcm/      # 临时PCM文件（自动创建）
├── temp_noise_pcm/      # 临时PCM文件（自动创建）
├── prepare_training_data.py  # 数据准备脚本
├── simple_train.py      # 简化训练脚本
├── run_training.py      # 完整训练流程脚本
└── TRAINING_README.md   # 本文件
```

## 快速开始

### 方法1: 一键训练（推荐）

```bash
# 使用简化特征提取进行快速测试
python run_training.py --simple --max-files 10 --epochs 20

# 使用更多文件和轮次进行完整训练
python run_training.py --max-files 50 --epochs 100
```

### 方法2: 分步执行

#### 步骤1: 准备训练数据

```bash
# 使用简化特征提取（推荐用于测试）
python prepare_training_data.py --simple --max-files 20

# 或使用完整特征提取（需要编译dump_features）
python prepare_training_data.py --max-files 50 --count 1000
```

#### 步骤2: 训练模型

```bash
python simple_train.py --epochs 50 --batch-size 16
```

## 参数说明

### run_training.py 参数

- `--clean-dir`: 清晰语音WAV文件目录（默认: clean_voice）
- `--noise-dir`: 噪声语音WAV文件目录（默认: noise_voice）
- `--model-dir`: 模型保存目录（默认: model）
- `--max-files`: 最大处理文件数（默认: 20）
- `--epochs`: 训练轮次（默认: 30）
- `--batch-size`: 批次大小（默认: 8）
- `--simple`: 使用简化特征提取
- `--skip-data-prep`: 跳过数据准备步骤

### prepare_training_data.py 参数

- `--clean-dir`: 清晰语音WAV文件目录
- `--noise-dir`: 噪声语音WAV文件目录
- `--output-dir`: 特征输出目录（默认: features）
- `--sample-rate`: 采样率（默认: 48000）
- `--max-files`: 最大处理文件数
- `--count`: 生成的训练序列数量
- `--simple`: 使用简化特征提取

### simple_train.py 参数

- `--features`: 特征文件路径
- `--model-dir`: 模型保存目录
- `--epochs`: 训练轮次
- `--batch-size`: 批次大小
- `--lr`: 学习率

## 训练数据要求

### WAV文件要求

1. **格式**: WAV格式，任意采样率（会自动转换为48kHz）
2. **声道**: 单声道或立体声（会自动转换为单声道）
3. **时长**: 建议每个文件至少3秒
4. **数量**: 建议至少10-50个文件用于测试，100+文件用于实际训练

### 文件组织

- `clean_voice/`: 放置清晰的语音文件
- `noise_voice/`: 放置对应的噪声语音文件（可以是clean_voice加噪声后的版本）

## 训练流程说明

### 1. WAV转PCM
- 使用ffmpeg将WAV文件转换为16位PCM格式
- 统一采样率为48kHz，单声道

### 2. 特征提取
- **简化模式**: 生成随机特征用于快速测试
- **完整模式**: 使用dump_features程序提取真实音频特征

### 3. 模型训练
- 使用PyTorch训练RNNoise模型
- 支持GPU加速（如果可用）
- 自动保存检查点和最佳模型

## 输出文件

训练完成后，`model/`目录将包含：

- `best_model.pth`: 最佳模型（损失最低）
- `rnnoise_custom_*.pth`: 各轮次的检查点
- 每个.pth文件包含完整的模型状态和参数

## 故障排除

### 常见问题

1. **"找不到ffmpeg"**
   - 确保env/ffmpeg.exe存在
   - 或修改脚本中的ffmpeg路径

2. **"数据集为空"**
   - 检查WAV文件是否存在于指定目录
   - 确保文件格式正确
   - 尝试减少max-files参数

3. **"内存不足"**
   - 减少batch-size参数
   - 减少max-files参数
   - 使用--simple模式

4. **"找不到dump_features"**
   - 使用--simple参数跳过dump_features
   - 或编译dump_features程序

### 编译dump_features（可选）

如果要使用完整特征提取：

```bash
cd src
gcc -o dump_features dump_features.c denoise.c kiss_fft.c celt_lpc.c pitch.c nnet.c rnn.c parse_lpcnet_weights.c -lm
```

## 性能建议

### 快速测试
```bash
python run_training.py --simple --max-files 5 --epochs 10 --batch-size 4
```

### 中等规模训练
```bash
python run_training.py --simple --max-files 20 --epochs 50 --batch-size 8
```

### 大规模训练
```bash
python run_training.py --max-files 100 --epochs 200 --batch-size 16
```

## 后续步骤

训练完成后，您可以：

1. **转换为C代码**（用于集成到C项目）:
   ```bash
   python torch/rnnoise/dump_rnnoise_weights.py --quantize model/best_model.pth rnnoise_c
   ```

2. **继续训练**:
   ```bash
   python simple_train.py --features features/training_features.f32 --epochs 100
   ```

3. **使用模型进行推理**:
   加载best_model.pth进行音频降噪

## 注意事项

1. 训练时间取决于数据量和硬件配置
2. GPU训练比CPU快很多
3. 建议先用少量数据测试流程
4. 定期检查训练损失，避免过拟合
5. 保存好训练数据和模型文件
