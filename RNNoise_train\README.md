# RNNoise 完整训练系统

本文件夹包含完整的RNNoise自定义模型训练系统，使用原始的RNNoise训练代码，不进行任何简化。

## 📁 文件结构

```
RNNoise_train/
├── clean_PCM_data/          # 清晰语音PCM文件 (自动生成)
├── noise_PCM_data/          # 噪声语音PCM文件 (自动生成)
├── features/                # 训练特征文件 (自动生成)
├── model_output/            # 训练输出目录 (自动生成)
│   └── checkpoints/         # 模型检查点
├── prepare_pcm_data.py      # 数据准备脚本
├── train_with_original.py   # 原始训练包装脚本
├── run_full_training.py     # 完整训练流程
└── README.md               # 本文件
```

## 🚀 快速开始

### 一键训练 (推荐)

```bash
cd RNNoise_train
python run_full_training.py --max-files 50 --epochs 200 --count 2000
```

### 分步执行

#### 步骤1: 准备PCM数据
```bash
python prepare_pcm_data.py --max-files 50 --count 2000
```

#### 步骤2: 使用原始代码训练
```bash
python train_with_original.py --epochs 200 --batch-size 128
```

## 📋 详细说明

### 数据准备 (prepare_pcm_data.py)

**功能**:
- 将WAV文件转换为48kHz单声道PCM格式
- 使用原始`dump_features`程序提取特征
- 生成符合原始训练格式的.f32特征文件

**参数**:
```bash
--clean-wav-dir     # 清晰语音WAV目录 (默认: ../clean_voice)
--noise-wav-dir     # 噪声语音WAV目录 (默认: ../noise_voice)
--clean-pcm-dir     # 清晰语音PCM输出目录 (默认: clean_PCM_data)
--noise-pcm-dir     # 噪声语音PCM输出目录 (默认: noise_PCM_data)
--features-file     # 特征文件输出路径 (默认: features/training_features.f32)
--max-files         # 最大处理文件数 (默认: 100)
--count             # 生成的训练序列数量 (默认: 2000)
--sample-rate       # PCM采样率 (默认: 48000)
--skip-conversion   # 跳过WAV到PCM转换
```

### 训练 (train_with_original.py)

**功能**:
- 直接调用原始`../torch/rnnoise/train_rnnoise.py`
- 保持所有原始训练逻辑和参数
- 实时显示训练进度

**参数** (与原始train_rnnoise.py完全一致):
```bash
--features          # 特征文件路径
--output            # 模型输出目录
--epochs            # 训练轮次 (默认: 200)
--batch-size        # 批次大小 (默认: 128)
--lr                # 学习率 (默认: 1e-3)
--cond-size         # 条件层大小 (默认: 128)
--gru-size          # GRU层大小 (默认: 384)
--sequence-length   # 序列长度 (默认: 2000)
--lr-decay          # 学习率衰减 (默认: 5e-5)
--gamma             # 感知指数 (默认: 0.25)
--suffix            # 模型名称后缀 (默认: _custom)
--sparse            # 启用稀疏化训练
```

### 完整流程 (run_full_training.py)

**功能**:
- 自动执行完整的训练流程
- 数据准备 → 特征提取 → 模型训练
- 验证结果并显示文件位置

## 🔧 环境要求

### 必需程序
1. **ffmpeg**: 用于WAV到PCM转换
   - 位置: `../env/ffmpeg.exe`
   
2. **dump_features**: 用于特征提取
   - 位置: `../src/dump_features.exe` 或 `../src/dump_features`
   - 如果不存在，脚本会尝试自动编译

### Python依赖
- PyTorch
- NumPy
- tqdm

### 编译dump_features (如果需要)
```bash
cd ../src
gcc -o dump_features dump_features.c denoise.c kiss_fft.c celt_lpc.c pitch.c nnet.c rnn.c parse_lpcnet_weights.c -lm -O2
```

## 📊 训练数据格式

### 输入数据
- **WAV文件**: 任意采样率，单声道或立体声
- **目录结构**:
  ```
  ../clean_voice/    # 清晰语音WAV文件
  ../noise_voice/    # 噪声语音WAV文件
  ```

### 处理后数据
- **PCM格式**: 48kHz, 16位, 单声道
- **特征格式**: .f32二进制文件，98维特征 (65特征 + 32增益 + 1VAD)
- **序列长度**: 2000帧

## 🎯 训练配置建议

### 快速测试
```bash
python run_full_training.py --max-files 10 --epochs 50 --count 500
```

### 标准训练
```bash
python run_full_training.py --max-files 50 --epochs 200 --count 2000
```

### 大规模训练
```bash
python run_full_training.py --max-files 100 --epochs 500 --count 5000 --sparse
```

## 📈 输出文件

### PCM数据
- `clean_PCM_data/`: 清晰语音PCM文件
- `noise_PCM_data/`: 噪声语音PCM文件

### 特征文件
- `features/training_features.f32`: 训练特征数据

### 模型文件
- `model_output/checkpoints/`: 训练检查点
  - `rnnoise_custom_1.pth`
  - `rnnoise_custom_2.pth`
  - ...
  - `rnnoise_custom_<epochs>.pth`

## 🔍 故障排除

### 常见问题

1. **"找不到dump_features"**
   ```bash
   cd ../src
   gcc -o dump_features dump_features.c denoise.c kiss_fft.c celt_lpc.c pitch.c nnet.c rnn.c parse_lpcnet_weights.c -lm -O2
   ```

2. **"找不到ffmpeg"**
   - 确保 `../env/ffmpeg.exe` 存在
   - 或修改脚本中的ffmpeg路径

3. **"内存不足"**
   - 减少批次大小: `--batch-size 64`
   - 减少序列长度: `--sequence-length 1000`
   - 减少文件数量: `--max-files 20`

4. **"特征文件为空"**
   - 检查PCM文件是否正确生成
   - 确保dump_features程序可执行
   - 检查文件路径是否正确

### 验证步骤

1. **检查PCM转换**:
   ```bash
   ls -la clean_PCM_data/
   ls -la noise_PCM_data/
   ```

2. **检查特征文件**:
   ```bash
   ls -la features/training_features.f32
   ```

3. **检查模型输出**:
   ```bash
   ls -la model_output/checkpoints/
   ```

## 🎉 训练完成后

### 模型转换为C代码
```bash
python ../torch/rnnoise/dump_rnnoise_weights.py --quantize model_output/checkpoints/rnnoise_custom_200.pth output_c
```

### 继续训练
```bash
python train_with_original.py --features features/training_features.f32 --epochs 300 --initial-checkpoint model_output/checkpoints/rnnoise_custom_200.pth
```

---

**注意**: 本训练系统完全使用原始RNNoise代码，确保训练结果的准确性和兼容性。
