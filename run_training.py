#!/usr/bin/env python3
"""
完整的RNNoise训练流程
整合数据准备和模型训练
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n=== {description} ===")
    print(f"运行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("成功完成")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"失败: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        return False

def main():
    parser = argparse.ArgumentParser(description='完整的RNNoise训练流程')
    parser.add_argument('--clean-dir', default='clean_voice',
                       help='清晰语音WAV文件目录')
    parser.add_argument('--noise-dir', default='noise_voice',
                       help='噪声语音WAV文件目录')
    parser.add_argument('--model-dir', default='model',
                       help='模型保存目录')
    parser.add_argument('--max-files', type=int, default=20,
                       help='最大处理文件数')
    parser.add_argument('--epochs', type=int, default=30,
                       help='训练轮次')
    parser.add_argument('--batch-size', type=int, default=8,
                       help='批次大小')
    parser.add_argument('--simple', action='store_true',
                       help='使用简化特征提取')
    parser.add_argument('--skip-data-prep', action='store_true',
                       help='跳过数据准备步骤')
    
    args = parser.parse_args()
    
    print("=== RNNoise完整训练流程 ===")
    print(f"清晰语音目录: {args.clean_dir}")
    print(f"噪声语音目录: {args.noise_dir}")
    print(f"模型保存目录: {args.model_dir}")
    print(f"最大文件数: {args.max_files}")
    print(f"训练轮次: {args.epochs}")
    print(f"批次大小: {args.batch_size}")
    
    # 检查输入目录
    if not os.path.exists(args.clean_dir):
        print(f"错误: 清晰语音目录不存在: {args.clean_dir}")
        return
    
    if not os.path.exists(args.noise_dir):
        print(f"错误: 噪声语音目录不存在: {args.noise_dir}")
        return
    
    # 步骤1: 数据准备
    if not args.skip_data_prep:
        print("\n步骤1: 准备训练数据")
        
        data_prep_cmd = [
            sys.executable, 'prepare_training_data.py',
            '--clean-dir', args.clean_dir,
            '--noise-dir', args.noise_dir,
            '--max-files', str(args.max_files),
            '--count', str(200)  # 生成较少的序列用于快速测试
        ]
        
        if args.simple:
            data_prep_cmd.append('--simple')
        
        if not run_command(data_prep_cmd, "数据准备"):
            print("数据准备失败，退出")
            return
    else:
        print("跳过数据准备步骤")
    
    # 检查特征文件是否存在
    features_file = "features/training_features.f32"
    if not os.path.exists(features_file):
        print(f"错误: 特征文件不存在: {features_file}")
        print("请先运行数据准备步骤")
        return
    
    # 步骤2: 模型训练
    print("\n步骤2: 训练模型")
    
    train_cmd = [
        sys.executable, 'simple_train.py',
        '--features', features_file,
        '--model-dir', args.model_dir,
        '--epochs', str(args.epochs),
        '--batch-size', str(args.batch_size)
    ]
    
    if not run_command(train_cmd, "模型训练"):
        print("模型训练失败")
        return
    
    # 步骤3: 验证结果
    print("\n步骤3: 验证训练结果")
    
    if os.path.exists(args.model_dir):
        model_files = [f for f in os.listdir(args.model_dir) if f.endswith('.pth')]
        if model_files:
            print(f"训练完成！生成了 {len(model_files)} 个模型文件:")
            for file in sorted(model_files):
                file_path = os.path.join(args.model_dir, file)
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                print(f"  {file} ({size_mb:.2f} MB)")
            
            # 显示最佳模型信息
            best_model = os.path.join(args.model_dir, 'best_model.pth')
            if os.path.exists(best_model):
                print(f"\n最佳模型: best_model.pth")
                print("可以使用此模型进行推理或进一步训练")
        else:
            print("警告: 没有找到生成的模型文件")
    else:
        print("错误: 模型目录不存在")
    
    print("\n=== 训练流程完成 ===")
    print("\n后续步骤:")
    print("1. 如果需要转换为C代码，可以使用:")
    print(f"   python torch/rnnoise/dump_rnnoise_weights.py --quantize {args.model_dir}/best_model.pth rnnoise_c")
    print("2. 如果需要继续训练，可以调整参数重新运行")
    print("3. 可以使用训练好的模型进行音频降噪测试")

if __name__ == "__main__":
    main()
