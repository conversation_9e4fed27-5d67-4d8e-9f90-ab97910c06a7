#!/usr/bin/env python3
"""
完整的RNNoise训练流程
1. WAV转PCM
2. 特征提取
3. 使用原始train_rnnoise.py训练
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_command(cmd, description, cwd=None):
    """运行命令并显示结果"""
    print(f"\n=== {description} ===")
    print(f"运行命令: {' '.join(cmd)}")
    if cwd:
        print(f"工作目录: {cwd}")
    
    try:
        if cwd:
            result = subprocess.run(cmd, cwd=cwd, check=True, text=True)
        else:
            result = subprocess.run(cmd, check=True, text=True)
        print("✓ 成功完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='完整的RNNoise训练流程')
    
    # 数据路径
    parser.add_argument('--clean-wav-dir', default='../clean_voice',
                       help='清晰语音WAV文件目录')
    parser.add_argument('--noise-wav-dir', default='../noise_voice',
                       help='噪声语音WAV文件目录')
    
    # 数据处理参数
    parser.add_argument('--max-files', type=int, default=50,
                       help='最大处理文件数')
    parser.add_argument('--count', type=int, default=2000,
                       help='生成的训练序列数量')
    parser.add_argument('--sample-rate', type=int, default=48000,
                       help='PCM采样率')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=200,
                       help='训练轮次')
    parser.add_argument('--batch-size', type=int, default=128,
                       help='批次大小')
    parser.add_argument('--lr', type=float, default=1e-3,
                       help='学习率')
    parser.add_argument('--cond-size', type=int, default=128,
                       help='条件层大小')
    parser.add_argument('--gru-size', type=int, default=384,
                       help='GRU层大小')
    parser.add_argument('--sparse', action='store_true',
                       help='启用稀疏化训练')
    
    # 流程控制
    parser.add_argument('--skip-data-prep', action='store_true',
                       help='跳过数据准备步骤')
    parser.add_argument('--skip-conversion', action='store_true',
                       help='跳过WAV到PCM转换')
    
    args = parser.parse_args()
    
    print("=== RNNoise完整训练流程 ===")
    print(f"清晰语音目录: {args.clean_wav_dir}")
    print(f"噪声语音目录: {args.noise_wav_dir}")
    print(f"最大文件数: {args.max_files}")
    print(f"训练序列数: {args.count}")
    print(f"训练轮次: {args.epochs}")
    print(f"批次大小: {args.batch_size}")
    print(f"稀疏化: {args.sparse}")
    
    # 检查输入目录
    if not args.skip_data_prep:
        if not os.path.exists(args.clean_wav_dir):
            print(f"✗ 清晰语音目录不存在: {args.clean_wav_dir}")
            return
        
        if not os.path.exists(args.noise_wav_dir):
            print(f"✗ 噪声语音目录不存在: {args.noise_wav_dir}")
            return
        
        print(f"✓ 输入目录检查通过")
    
    # 步骤1: 数据准备
    if not args.skip_data_prep:
        print("\n" + "="*50)
        print("步骤1: 数据准备 (WAV转PCM + 特征提取)")
        print("="*50)
        
        data_prep_cmd = [
            sys.executable, 'prepare_pcm_data.py',
            '--clean-wav-dir', args.clean_wav_dir,
            '--noise-wav-dir', args.noise_wav_dir,
            '--max-files', str(args.max_files),
            '--count', str(args.count),
            '--sample-rate', str(args.sample_rate)
        ]
        
        if args.skip_conversion:
            data_prep_cmd.append('--skip-conversion')
        
        if not run_command(data_prep_cmd, "数据准备"):
            print("数据准备失败，退出")
            return
    else:
        print("跳过数据准备步骤")
    
    # 检查特征文件
    features_file = "features/training_features.f32"
    if not os.path.exists(features_file):
        print(f"✗ 特征文件不存在: {features_file}")
        print("请先运行数据准备步骤")
        return
    
    file_size = os.path.getsize(features_file) / (1024 * 1024)
    print(f"✓ 特征文件: {features_file} ({file_size:.2f} MB)")
    
    # 步骤2: 模型训练
    print("\n" + "="*50)
    print("步骤2: 模型训练 (使用原始train_rnnoise.py)")
    print("="*50)
    
    train_cmd = [
        sys.executable, 'train_with_original.py',
        '--features', features_file,
        '--output', 'model_output',
        '--epochs', str(args.epochs),
        '--batch-size', str(args.batch_size),
        '--lr', str(args.lr),
        '--cond-size', str(args.cond_size),
        '--gru-size', str(args.gru_size),
        '--suffix', '_custom'
    ]
    
    if args.sparse:
        train_cmd.append('--sparse')
    
    if not run_command(train_cmd, "模型训练"):
        print("模型训练失败")
        return
    
    # 步骤3: 验证结果
    print("\n" + "="*50)
    print("步骤3: 验证训练结果")
    print("="*50)
    
    model_dir = "model_output"
    checkpoints_dir = os.path.join(model_dir, "checkpoints")
    
    if os.path.exists(checkpoints_dir):
        model_files = [f for f in os.listdir(checkpoints_dir) if f.endswith('.pth')]
        if model_files:
            print(f"✓ 训练完成！生成了 {len(model_files)} 个模型文件:")
            for file in sorted(model_files):
                file_path = os.path.join(checkpoints_dir, file)
                size_mb = os.path.getsize(file_path) / (1024 * 1024)
                print(f"  {file} ({size_mb:.2f} MB)")
            
            print(f"\n模型文件位置: {checkpoints_dir}")
        else:
            print("✗ 没有找到生成的模型文件")
    else:
        print("✗ 检查点目录不存在")
    
    # 显示PCM数据信息
    print(f"\nPCM数据位置:")
    clean_pcm_dir = "clean_PCM_data"
    noise_pcm_dir = "noise_PCM_data"
    
    if os.path.exists(clean_pcm_dir):
        clean_count = len([f for f in os.listdir(clean_pcm_dir) if f.endswith('.pcm')])
        print(f"  清晰语音PCM: {clean_pcm_dir} ({clean_count} 个文件)")
    
    if os.path.exists(noise_pcm_dir):
        noise_count = len([f for f in os.listdir(noise_pcm_dir) if f.endswith('.pcm')])
        print(f"  噪声语音PCM: {noise_pcm_dir} ({noise_count} 个文件)")
    
    print("\n" + "="*50)
    print("训练流程完成！")
    print("="*50)
    
    print(f"\n生成的文件:")
    print(f"├── clean_PCM_data/     # 清晰语音PCM文件")
    print(f"├── noise_PCM_data/     # 噪声语音PCM文件")
    print(f"├── features/           # 训练特征文件")
    print(f"└── model_output/       # 训练好的模型")
    print(f"    └── checkpoints/    # 模型检查点")
    
    print(f"\n后续步骤:")
    print(f"1. 测试模型效果")
    print(f"2. 选择最佳检查点")
    print(f"3. 转换为C代码 (可选):")
    print(f"   python ../torch/rnnoise/dump_rnnoise_weights.py --quantize model_output/checkpoints/rnnoise_custom_<epoch>.pth output_c")

if __name__ == "__main__":
    main()
