#!/usr/bin/env python3
"""
完整的RNNoise数据准备脚本
使用原始的dump_features程序进行特征提取
"""

import os
import sys
import subprocess
import glob
import argparse
import numpy as np
import tqdm
from pathlib import Path
import shutil

class FullDataPreparator:
    """完整数据准备类 - 使用原始RNNoise流程"""
    
    def __init__(self, ffmpeg_path="../env/ffmpeg.exe"):
        self.ffmpeg_path = ffmpeg_path
        self.dump_features_exe = "../src/dump_features.exe"
        if not os.path.exists(self.dump_features_exe):
            self.dump_features_exe = "../src/dump_features"
        
    def wav_to_pcm(self, wav_file, pcm_file, sample_rate=48000):
        """将WAV文件转换为16位PCM格式"""
        cmd = [
            self.ffmpeg_path,
            '-i', wav_file,
            '-f', 's16le',  # 16位小端格式
            '-ar', str(sample_rate),  # 采样率
            '-ac', '1',  # 单声道
            '-y',  # 覆盖输出文件
            pcm_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"转换失败 {wav_file}: {e.stderr}")
            return False
    
    def process_wav_directory(self, input_dir, output_dir, sample_rate=48000, max_files=None):
        """批量处理目录中的WAV文件"""
        os.makedirs(output_dir, exist_ok=True)
        
        wav_files = sorted(glob.glob(os.path.join(input_dir, "*.wav")))
        if max_files:
            wav_files = wav_files[:max_files]
            
        print(f"找到 {len(wav_files)} 个WAV文件在 {input_dir}")
        
        success_files = []
        for wav_file in tqdm.tqdm(wav_files, desc=f"转换 {os.path.basename(input_dir)}"):
            filename = os.path.splitext(os.path.basename(wav_file))[0]
            pcm_file = os.path.join(output_dir, f"{filename}.pcm")
            
            if self.wav_to_pcm(wav_file, pcm_file, sample_rate):
                success_files.append(pcm_file)
        
        print(f"成功转换 {len(success_files)}/{len(wav_files)} 个文件")
        return success_files
    
    def create_file_list(self, pcm_files, list_file):
        """创建文件列表"""
        with open(list_file, 'w') as f:
            for pcm_file in pcm_files:
                f.write(f"{os.path.abspath(pcm_file)}\n")
        return list_file
    
    def compile_dump_features(self):
        """编译dump_features程序"""
        print("检查dump_features程序...")
        
        if os.path.exists(self.dump_features_exe):
            print(f"找到dump_features程序: {self.dump_features_exe}")
            return True
        
        print("dump_features程序不存在，尝试编译...")
        
        # 检查源文件
        src_dir = "../src"
        required_files = [
            "dump_features.c", "denoise.c", "kiss_fft.c", 
            "celt_lpc.c", "pitch.c", "nnet.c", "rnn.c", 
            "parse_lpcnet_weights.c"
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(os.path.join(src_dir, file)):
                missing_files.append(file)
        
        if missing_files:
            print(f"缺少源文件: {missing_files}")
            return False
        
        # 编译命令
        compile_cmd = [
            "gcc", "-o", "dump_features",
            "dump_features.c", "denoise.c", "kiss_fft.c",
            "celt_lpc.c", "pitch.c", "nnet.c", "rnn.c",
            "parse_lpcnet_weights.c", "-lm", "-O2"
        ]
        
        try:
            print(f"编译命令: {' '.join(compile_cmd)}")
            result = subprocess.run(compile_cmd, cwd=src_dir, 
                                  capture_output=True, text=True, check=True)
            print("编译成功！")
            return True
        except subprocess.CalledProcessError as e:
            print(f"编译失败: {e.stderr}")
            print("请手动编译dump_features程序")
            return False
    
    def extract_features_with_dump(self, clean_files, noise_files, output_file, count=1000):
        """使用dump_features程序提取特征"""
        
        # 检查并编译dump_features
        if not self.compile_dump_features():
            return False
        
        # 确保有足够的文件
        min_files = min(len(clean_files), len(noise_files))
        if min_files == 0:
            print("错误: 没有找到PCM文件")
            return False
        
        print(f"使用 {min_files} 对文件进行特征提取")
        
        # 创建临时文件列表
        clean_list = "temp_clean_list.txt"
        noise_list = "temp_noise_list.txt"
        empty_list = "temp_empty_list.txt"
        
        # 创建文件列表
        self.create_file_list(clean_files[:min_files], clean_list)
        self.create_file_list(noise_files[:min_files], noise_list)
        
        # 创建空的前景噪声列表
        with open(empty_list, 'w') as f:
            f.write("")
        
        # 创建输出目录
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 运行dump_features
        cmd = [
            self.dump_features_exe,
            clean_list,
            noise_list,
            empty_list,  # 前景噪声文件列表（空）
            output_file,
            str(count)
        ]
        
        print(f"运行特征提取: {' '.join(cmd)}")
        print(f"生成 {count} 个训练序列...")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("特征提取完成！")
            if result.stdout:
                print(f"输出: {result.stdout}")
            
            # 清理临时文件
            for temp_file in [clean_list, noise_list, empty_list]:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            
            # 验证输出文件
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"特征文件大小: {file_size / (1024*1024):.2f} MB")
                
                # 计算预期的数据量
                # 每个序列: 2000帧 × 98维 × 4字节 = 784,000 字节
                expected_size = count * 2000 * 98 * 4
                print(f"预期大小: {expected_size / (1024*1024):.2f} MB")
                
                if file_size > 0:
                    print("✓ 特征提取成功！")
                    return True
                else:
                    print("✗ 特征文件为空")
                    return False
            else:
                print("✗ 特征文件未生成")
                return False
            
        except subprocess.CalledProcessError as e:
            print(f"特征提取失败: {e.stderr}")
            if e.stdout:
                print(f"输出: {e.stdout}")
            return False
    
    def verify_features(self, features_file):
        """验证特征文件格式"""
        try:
            data = np.memmap(features_file, dtype='float32', mode='r')
            print(f"特征数据总大小: {data.shape}")
            
            # 计算序列数量
            dim = 98  # NB_FEATURES(65) + NB_BANDS(32) + VAD(1)
            sequence_length = 2000
            
            total_elements = data.shape[0]
            expected_sequences = total_elements // (sequence_length * dim)
            
            print(f"预计序列数量: {expected_sequences}")
            print(f"每序列维度: {dim}")
            print(f"序列长度: {sequence_length}")
            
            if expected_sequences > 0:
                print("✓ 特征文件格式正确")
                return True
            else:
                print("✗ 特征文件格式错误")
                return False
                
        except Exception as e:
            print(f"验证特征文件失败: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='完整的RNNoise数据准备')
    parser.add_argument('--clean-dir', default='../clean_voice', 
                       help='清晰语音WAV文件目录')
    parser.add_argument('--noise-dir', default='../noise_voice', 
                       help='噪声语音WAV文件目录')
    parser.add_argument('--output-file', default='features/training_features.f32', 
                       help='特征输出文件')
    parser.add_argument('--sample-rate', type=int, default=48000, 
                       help='采样率')
    parser.add_argument('--max-files', type=int, default=100, 
                       help='最大处理文件数')
    parser.add_argument('--count', type=int, default=2000, 
                       help='生成的训练序列数量')
    
    args = parser.parse_args()
    
    print("=== 完整RNNoise数据准备流程 ===")
    print(f"清晰语音目录: {args.clean_dir}")
    print(f"噪声语音目录: {args.noise_dir}")
    print(f"输出文件: {args.output_file}")
    print(f"序列数量: {args.count}")
    
    preparator = FullDataPreparator()
    
    # 步骤1: 转换WAV到PCM
    print("\n步骤1: 转换WAV文件为PCM格式")
    
    clean_pcm_dir = "temp_clean_pcm"
    noise_pcm_dir = "temp_noise_pcm"
    
    clean_files = preparator.process_wav_directory(
        args.clean_dir, clean_pcm_dir, args.sample_rate, args.max_files)
    noise_files = preparator.process_wav_directory(
        args.noise_dir, noise_pcm_dir, args.sample_rate, args.max_files)
    
    if not clean_files or not noise_files:
        print("错误: 没有成功转换任何文件")
        return
    
    # 步骤2: 使用dump_features提取特征
    print("\n步骤2: 使用dump_features提取特征")
    
    success = preparator.extract_features_with_dump(
        clean_files, noise_files, args.output_file, args.count)
    
    if success:
        print("\n步骤3: 验证特征文件")
        preparator.verify_features(args.output_file)
        
        print(f"\n✓ 数据准备完成！")
        print(f"特征文件: {args.output_file}")
        print("现在可以使用原始的train_rnnoise.py进行训练")
        print(f"训练命令: python ../torch/rnnoise/train_rnnoise.py {args.output_file} model_output")
    else:
        print("\n✗ 数据准备失败！")

if __name__ == "__main__":
    main()
