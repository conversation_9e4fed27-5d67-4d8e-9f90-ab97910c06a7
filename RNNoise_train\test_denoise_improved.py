#!/usr/bin/env python3
"""
改进的RNNoise降噪脚本 - 更接近原始算法
"""

import os
import sys
import numpy as np
import torch
import scipy.signal
import tempfile
import subprocess

# 配置区域
INPUT_AUDIO_FILE = "D:/RNNoise/rnnoise-plus-main/RNNoise_train/test_voice/20250716_084131.wav"
OUTPUT_AUDIO_FILE = "denoised_improved.wav"
MODEL_FILE_PATH = "D:/RNNoise/rnnoise-plus-main/RNNoise_train/model_output/checkpoints/rnnoise_best.pth"

# 添加torch/rnnoise到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'torch', 'rnnoise'))
import rnnoise

class ImprovedRNNoiseProcessor:
    """改进的RNNoise处理器"""
    
    def __init__(self):
        self.sample_rate = 48000
        self.frame_size = 480  # 10ms
        self.hop_size = 240    # 50% overlap
        self.fft_size = 512
        self.window = np.hanning(self.frame_size)
        
    def load_audio(self, wav_file):
        """加载WAV音频"""
        # 使用ffmpeg转换
        with tempfile.NamedTemporaryFile(suffix='.pcm', delete=False) as tmp:
            cmd = [
                'ffmpeg', '-i', wav_file,
                '-f', 's16le', '-ar', str(self.sample_rate), '-ac', '1',
                '-y', tmp.name
            ]
            try:
                subprocess.run(cmd, capture_output=True, check=True)
                with open(tmp.name, 'rb') as f:
                    data = np.frombuffer(f.read(), dtype=np.int16)
                os.unlink(tmp.name)
                return data.astype(np.float32) / 32768.0
            except:
                print("使用scipy加载音频")
                from scipy.io import wavfile
                sr, data = wavfile.read(wav_file)
                if len(data.shape) > 1:
                    data = data[:, 0]  # 取第一声道
                if sr != self.sample_rate:
                    # 重采样到48kHz
                    data = scipy.signal.resample(data, int(len(data) * self.sample_rate / sr))
                return data.astype(np.float32) / 32768.0
    
    def save_audio(self, audio_data, wav_file):
        """保存WAV音频"""
        with tempfile.NamedTemporaryFile(suffix='.pcm', delete=False) as tmp:
            audio_int16 = (audio_data * 32768.0).astype(np.int16)
            with open(tmp.name, 'wb') as f:
                f.write(audio_int16.tobytes())
            
            cmd = [
                'ffmpeg', '-f', 's16le', '-ar', str(self.sample_rate), '-ac', '1',
                '-i', tmp.name, '-y', wav_file
            ]
            try:
                subprocess.run(cmd, capture_output=True, check=True)
                os.unlink(tmp.name)
                return True
            except:
                print("保存失败")
                return False
    
    def extract_rnnoise_features(self, frame):
        """提取RNNoise特征 - 更接近原始算法"""
        # 应用窗函数
        windowed_frame = frame * self.window
        
        # FFT
        fft_data = np.fft.fft(windowed_frame, n=self.fft_size)
        magnitude = np.abs(fft_data[:self.fft_size//2])
        phase = np.angle(fft_data[:self.fft_size//2])
        
        # Bark频谱 (22维)
        bark_bands = 22
        bark_spectrum = np.zeros(bark_bands)
        for i in range(bark_bands):
            start_bin = int(i * len(magnitude) / bark_bands)
            end_bin = int((i + 1) * len(magnitude) / bark_bands)
            bark_spectrum[i] = np.log(np.mean(magnitude[start_bin:end_bin]) + 1e-10)
        
        # 基音特征 (6维)
        pitch_features = np.zeros(6)
        # 自相关
        autocorr = np.correlate(frame, frame, mode='full')
        autocorr = autocorr[len(autocorr)//2:]
        
        min_period, max_period = 20, 320
        if len(autocorr) > max_period:
            pitch_corr = autocorr[min_period:max_period]
            if len(pitch_corr) > 0:
                pitch_period = np.argmax(pitch_corr) + min_period
                pitch_gain = pitch_corr[np.argmax(pitch_corr)] / (autocorr[0] + 1e-10)
                pitch_features[0] = pitch_period / 320.0
                pitch_features[1] = np.clip(pitch_gain, 0, 1)
                pitch_features[2] = np.log(np.var(frame) + 1e-10)
                pitch_features[3] = np.log(np.mean(np.abs(frame)) + 1e-10)
                pitch_features[4] = np.log(np.sum(frame ** 2) + 1e-10)
                pitch_features[5] = np.log(np.max(np.abs(frame)) + 1e-10)
        
        # 频谱特征 (37维)
        spectral_features = np.zeros(37)
        
        # 频谱质心、带宽等
        freqs = np.arange(len(magnitude)) * self.sample_rate / self.fft_size
        spectral_centroid = np.sum(freqs * magnitude) / (np.sum(magnitude) + 1e-10)
        spectral_features[0] = spectral_centroid / (self.sample_rate / 2)
        
        # 频谱滚降
        cumsum_mag = np.cumsum(magnitude)
        rolloff_idx = np.where(cumsum_mag >= 0.85 * cumsum_mag[-1])[0]
        if len(rolloff_idx) > 0:
            spectral_features[1] = rolloff_idx[0] / len(magnitude)
        
        # 频带能量 (32维)
        for i in range(32):
            start_bin = int(i * len(magnitude) / 32)
            end_bin = int((i + 1) * len(magnitude) / 32)
            spectral_features[5 + i] = np.log(np.mean(magnitude[start_bin:end_bin]) + 1e-10)
        
        # 其他特征
        spectral_features[2] = np.log(np.sum(magnitude) + 1e-10)  # 总能量
        spectral_features[3] = np.log(np.std(magnitude) + 1e-10)  # 频谱标准差
        spectral_features[4] = np.mean(magnitude) / (np.max(magnitude) + 1e-10)  # 频谱平坦度
        
        # 组合特征 (65维)
        features = np.concatenate([bark_spectrum, pitch_features, spectral_features])
        return features[:65]  # 确保65维
    
    def apply_spectral_gain(self, frame, gain):
        """在频域应用增益"""
        # 应用窗函数
        windowed_frame = frame * self.window
        
        # FFT
        fft_data = np.fft.fft(windowed_frame, n=self.fft_size)
        magnitude = np.abs(fft_data)
        phase = np.angle(fft_data)
        
        # 应用增益到频谱
        enhanced_magnitude = magnitude.copy()
        for i in range(32):
            start_bin = int(i * self.fft_size // 2 / 32)
            end_bin = int((i + 1) * self.fft_size // 2 / 32)
            
            # 应用增益，但保持相位
            band_gain = np.clip(gain[i], 0.1, 1.0)  # 限制增益范围
            enhanced_magnitude[start_bin:end_bin] *= band_gain
            # 对称部分
            if start_bin > 0:
                enhanced_magnitude[self.fft_size - end_bin:self.fft_size - start_bin] *= band_gain
        
        # 重构信号
        enhanced_fft = enhanced_magnitude * np.exp(1j * phase)
        enhanced_frame = np.real(np.fft.ifft(enhanced_fft))[:self.frame_size]
        
        return enhanced_frame
    
    def denoise_audio(self, audio_data, model):
        """对整个音频进行降噪"""
        # 填充音频以便处理
        pad_size = self.frame_size
        padded_audio = np.pad(audio_data, (pad_size, pad_size), mode='constant')
        
        # 输出音频
        output_audio = np.zeros_like(padded_audio)
        overlap_count = np.zeros_like(padded_audio)
        
        # 逐帧处理
        num_frames = (len(padded_audio) - self.frame_size) // self.hop_size + 1
        
        print(f"处理 {num_frames} 帧...")
        
        for i in range(num_frames):
            start_idx = i * self.hop_size
            end_idx = start_idx + self.frame_size
            
            if end_idx > len(padded_audio):
                break
            
            frame = padded_audio[start_idx:end_idx]
            
            # 提取特征
            features = self.extract_rnnoise_features(frame)
            
            # 模型推理 - 需要序列格式
            with torch.no_grad():
                # 创建200帧的序列，重复当前特征
                sequence_features = np.tile(features, (200, 1))  # (200, 65)
                features_tensor = torch.from_numpy(sequence_features).unsqueeze(0)  # (1, 200, 65)
                pred_gain, _, _ = model(features_tensor)
                # 取最后一帧的增益
                gain = pred_gain[0, -1, :].cpu().numpy()  # (32,)
            
            # 应用增益
            enhanced_frame = self.apply_spectral_gain(frame, gain)
            
            # 重叠加窗
            output_audio[start_idx:end_idx] += enhanced_frame * self.window
            overlap_count[start_idx:end_idx] += self.window
            
            if i % 100 == 0:
                print(f"已处理 {i}/{num_frames} 帧")
        
        # 归一化重叠区域
        overlap_count[overlap_count == 0] = 1
        output_audio = output_audio / overlap_count
        
        # 移除填充
        return output_audio[pad_size:-pad_size]

def main():
    print("=== 改进的RNNoise降噪测试 ===")
    
    # 检查文件
    if not os.path.exists(INPUT_AUDIO_FILE):
        print(f"错误: 输入文件不存在: {INPUT_AUDIO_FILE}")
        return
    
    if not os.path.exists(MODEL_FILE_PATH):
        print(f"错误: 模型文件不存在: {MODEL_FILE_PATH}")
        return
    
    # 创建处理器
    processor = ImprovedRNNoiseProcessor()
    
    # 加载模型
    print("加载模型...")
    checkpoint = torch.load(MODEL_FILE_PATH, map_location='cpu')
    model = rnnoise.RNNoise(**checkpoint.get('model_kwargs', {'cond_size': 128, 'gru_size': 384}))
    model.load_state_dict(checkpoint['state_dict'])
    model.eval()
    
    # 加载音频
    print("加载音频...")
    audio_data = processor.load_audio(INPUT_AUDIO_FILE)
    print(f"音频长度: {len(audio_data)} 样本 ({len(audio_data)/48000:.2f}秒)")
    
    # 降噪处理
    print("开始降噪...")
    denoised_audio = processor.denoise_audio(audio_data, model)
    
    # 保存结果
    print("保存结果...")
    if processor.save_audio(denoised_audio, OUTPUT_AUDIO_FILE):
        print(f"✓ 降噪完成！输出文件: {OUTPUT_AUDIO_FILE}")
    else:
        print("✗ 保存失败")

if __name__ == "__main__":
    main()
