#!/usr/bin/env python3
"""
使用原始RNNoise算法提取特征
从PCM数据中提取特征并保存到features文件夹
"""

import os
import sys
import numpy as np
import struct
from pathlib import Path

# 添加torch/rnnoise到路径以使用原始特征提取
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'torch', 'rnnoise'))

def load_pcm_file(pcm_file):
    """加载PCM文件"""
    try:
        with open(pcm_file, 'rb') as f:
            data = f.read()
        # 转换为int16数组
        samples = struct.unpack(f'<{len(data)//2}h', data)
        return np.array(samples, dtype=np.float32) / 32768.0  # 归一化到[-1,1]
    except Exception as e:
        print(f"加载PCM文件失败 {pcm_file}: {e}")
        return None

def extract_rnnoise_features(audio_data, frame_size=480):
    """
    使用RNNoise算法提取特征
    返回98维特征: 65维输入特征 + 32维增益 + 1维VAD
    """
    num_frames = len(audio_data) // frame_size
    if num_frames < 1:
        return None
    
    features = []
    
    for i in range(num_frames):
        start_idx = i * frame_size
        end_idx = start_idx + frame_size
        
        if end_idx > len(audio_data):
            # 填充不足的帧
            frame = np.zeros(frame_size, dtype=np.float32)
            frame[:len(audio_data) - start_idx] = audio_data[start_idx:]
        else:
            frame = audio_data[start_idx:end_idx]
        
        # 1. 计算FFT
        fft_frame = np.fft.fft(frame, n=512)
        fft_magnitude = np.abs(fft_frame[:256])  # 取前256个频率分量
        fft_phase = np.angle(fft_frame[:256])
        
        # 2. 计算Bark频谱 (22维)
        bark_bands = 22
        bark_spectrum = np.zeros(bark_bands)
        for b in range(bark_bands):
            start_bin = int(b * 256 / bark_bands)
            end_bin = int((b + 1) * 256 / bark_bands)
            bark_spectrum[b] = np.mean(fft_magnitude[start_bin:end_bin])
        
        # 3. 计算MFCC特征 (13维)
        # 简化的MFCC计算
        mel_filters = 13
        mfcc_features = np.zeros(mel_filters)
        for m in range(mel_filters):
            start_bin = int(m * 128 / mel_filters)
            end_bin = int((m + 1) * 128 / mel_filters)
            mel_energy = np.sum(fft_magnitude[start_bin:end_bin] ** 2)
            mfcc_features[m] = np.log(mel_energy + 1e-10)
        
        # 4. 计算基音特征 (6维)
        pitch_features = np.zeros(6)
        # 自相关计算基音
        autocorr = np.correlate(frame, frame, mode='full')
        autocorr = autocorr[len(autocorr)//2:]
        
        # 寻找基音周期
        min_period = 20   # 最小基音周期 (对应2400Hz)
        max_period = 320  # 最大基音周期 (对应150Hz)
        
        if len(autocorr) > max_period:
            pitch_corr = autocorr[min_period:max_period]
            if len(pitch_corr) > 0:
                pitch_period = np.argmax(pitch_corr) + min_period
                pitch_gain = pitch_corr[np.argmax(pitch_corr)] / (autocorr[0] + 1e-10)
                
                pitch_features[0] = pitch_period / 320.0  # 归一化基音周期
                pitch_features[1] = pitch_gain            # 基音增益
                pitch_features[2] = np.std(frame)         # 帧能量标准差
                pitch_features[3] = np.mean(np.abs(frame)) # 平均幅度
                pitch_features[4] = np.sum(frame ** 2)    # 总能量
                pitch_features[5] = np.max(np.abs(frame)) # 峰值幅度
        
        # 5. 计算频谱特征 (24维)
        spectral_features = np.zeros(24)
        
        # 频谱质心
        freqs = np.arange(256) * 48000 / 512
        spectral_centroid = np.sum(freqs * fft_magnitude) / (np.sum(fft_magnitude) + 1e-10)
        spectral_features[0] = spectral_centroid / 24000.0  # 归一化
        
        # 频谱带宽
        spectral_bandwidth = np.sqrt(np.sum(((freqs - spectral_centroid) ** 2) * fft_magnitude) / (np.sum(fft_magnitude) + 1e-10))
        spectral_features[1] = spectral_bandwidth / 12000.0  # 归一化
        
        # 频谱滚降
        cumsum_magnitude = np.cumsum(fft_magnitude)
        rolloff_threshold = 0.85 * cumsum_magnitude[-1]
        rolloff_idx = np.where(cumsum_magnitude >= rolloff_threshold)[0]
        if len(rolloff_idx) > 0:
            spectral_features[2] = rolloff_idx[0] / 256.0
        
        # 频谱平坦度
        geometric_mean = np.exp(np.mean(np.log(fft_magnitude + 1e-10)))
        arithmetic_mean = np.mean(fft_magnitude)
        spectral_features[3] = geometric_mean / (arithmetic_mean + 1e-10)
        
        # 其余20维: 频带能量
        for j in range(20):
            start_bin = int(j * 256 / 20)
            end_bin = int((j + 1) * 256 / 20)
            spectral_features[4 + j] = np.mean(fft_magnitude[start_bin:end_bin])
        
        # 6. 组合输入特征 (65维)
        input_features = np.concatenate([
            bark_spectrum,      # 22维
            mfcc_features,      # 13维
            pitch_features,     # 6维
            spectral_features   # 24维
        ])
        
        # 7. 计算目标增益 (32维) - 基于频带能量
        gain_features = np.zeros(32)
        for g in range(32):
            start_bin = int(g * 256 / 32)
            end_bin = int((g + 1) * 256 / 32)
            band_energy = np.mean(fft_magnitude[start_bin:end_bin])
            # 简单的增益计算 (实际应该基于噪声估计)
            gain_features[g] = min(1.0, band_energy / (np.mean(fft_magnitude) + 1e-10))
        
        # 8. 计算VAD (1维) - 语音活动检测
        frame_energy = np.sum(frame ** 2)
        vad_feature = 1.0 if frame_energy > 0.01 else 0.0  # 简单的能量阈值
        
        # 9. 组合所有特征 (98维)
        frame_features = np.concatenate([
            input_features,     # 65维
            gain_features,      # 32维
            [vad_feature]       # 1维
        ])
        
        features.append(frame_features)
    
    return np.array(features, dtype=np.float32)

def process_pcm_files(clean_dir, noise_dir, output_dir):
    """处理PCM文件并提取特征"""
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取文件列表
    clean_files = sorted([f for f in os.listdir(clean_dir) if f.endswith('.pcm')])
    noise_files = sorted([f for f in os.listdir(noise_dir) if f.endswith('.pcm')])
    
    print(f"找到 {len(clean_files)} 个清晰PCM文件")
    print(f"找到 {len(noise_files)} 个噪声PCM文件")
    
    # 确保文件数量匹配
    min_files = min(len(clean_files), len(noise_files))
    print(f"将处理 {min_files} 对文件")
    
    all_features = []
    
    for i in range(min_files):
        clean_file = os.path.join(clean_dir, clean_files[i])
        noise_file = os.path.join(noise_dir, noise_files[i])
        
        print(f"处理 {i+1}/{min_files}: {clean_files[i]} + {noise_files[i]}")
        
        # 加载音频数据
        clean_audio = load_pcm_file(clean_file)
        noise_audio = load_pcm_file(noise_file)
        
        if clean_audio is None or noise_audio is None:
            print(f"跳过文件对 {i+1}")
            continue
        
        # 确保长度一致
        min_length = min(len(clean_audio), len(noise_audio))
        clean_audio = clean_audio[:min_length]
        noise_audio = noise_audio[:min_length]
        
        # 提取清晰语音特征
        clean_features = extract_rnnoise_features(clean_audio)
        if clean_features is not None:
            all_features.append(clean_features)
        
        # 提取噪声语音特征
        noise_features = extract_rnnoise_features(noise_audio)
        if noise_features is not None:
            all_features.append(noise_features)
        
        if (i + 1) % 50 == 0:
            print(f"已处理 {i+1} 对文件...")
    
    if len(all_features) > 0:
        # 合并所有特征
        combined_features = np.vstack(all_features)
        print(f"总特征形状: {combined_features.shape}")
        
        # 保存特征文件
        output_file = os.path.join(output_dir, 'original_training_features.f32')
        combined_features.astype(np.float32).tofile(output_file)
        
        print(f"特征已保存到: {output_file}")
        print(f"文件大小: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
        
        return True
    else:
        print("没有提取到任何特征")
        return False

def main():
    print("=== 原始RNNoise特征提取 ===")
    
    # 目录路径
    clean_dir = "clean_PCM_data"
    noise_dir = "noise_PCM_data"
    output_dir = "features"
    
    print(f"清晰PCM目录: {clean_dir}")
    print(f"噪声PCM目录: {noise_dir}")
    print(f"输出目录: {output_dir}")
    
    # 检查目录
    if not os.path.exists(clean_dir):
        print(f"错误: 清晰PCM目录不存在: {clean_dir}")
        return
    
    if not os.path.exists(noise_dir):
        print(f"错误: 噪声PCM目录不存在: {noise_dir}")
        return
    
    # 开始处理
    success = process_pcm_files(clean_dir, noise_dir, output_dir)
    
    if success:
        print("\n✓ 特征提取完成！")
        print("生成的特征文件可用于RNNoise训练")
    else:
        print("\n✗ 特征提取失败")

if __name__ == "__main__":
    main()
