#!/usr/bin/env python3
"""
RNNoise模型测试脚本
输入WAV文件，输出降噪后的WAV文件
使用方法: python test_denoise.py input.wav output.wav
"""

# ==================== 配置区域 ====================
# 在这里修改默认的输入音频地址
DEFAULT_INPUT_WAV = "./test_voice/20250716_084323.wav"  # 修改这里来换测试音频
DEFAULT_OUTPUT_WAV = "denoised_output.wav"              # 默认输出文件名
# ================================================
import os
import sys
import numpy as np
import torch
import argparse
import subprocess
import tempfile

# 添加torch/rnnoise到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'torch', 'rnnoise'))
import rnnoise

class AudioProcessor:
    """音频处理类"""
    
    def __init__(self, ffmpeg_path="../env/ffmpeg.exe"):
        self.ffmpeg_path = ffmpeg_path
        self.sample_rate = 48000
        self.frame_size = 480  # 10ms @ 48kHz
        
    def wav_to_pcm(self, wav_file, pcm_file):
        """WAV转PCM"""
        cmd = [
            self.ffmpeg_path,
            '-i', wav_file,
            '-f', 's16le',  # 16位小端
            '-ar', str(self.sample_rate),  # 48kHz
            '-ac', '1',  # 单声道
            '-y',  # 覆盖输出
            pcm_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"WAV转PCM失败: {e.stderr}")
            return False
    
    def pcm_to_wav(self, pcm_file, wav_file):
        """PCM转WAV"""
        cmd = [
            self.ffmpeg_path,
            '-f', 's16le',  # 输入格式
            '-ar', str(self.sample_rate),  # 采样率
            '-ac', '1',  # 单声道
            '-i', pcm_file,
            '-y',  # 覆盖输出
            wav_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"PCM转WAV失败: {e.stderr}")
            return False
    
    def load_pcm(self, pcm_file):
        """加载PCM文件"""
        try:
            with open(pcm_file, 'rb') as f:
                data = np.frombuffer(f.read(), dtype=np.int16)
            # 转换为float32，范围[-1, 1]
            return data.astype(np.float32) / 32768.0
        except Exception as e:
            print(f"加载PCM失败: {e}")
            return None
    
    def save_pcm(self, audio_data, pcm_file):
        """保存PCM文件"""
        try:
            # 转换为int16
            audio_int16 = (audio_data * 32768.0).astype(np.int16)
            with open(pcm_file, 'wb') as f:
                f.write(audio_int16.tobytes())
            return True
        except Exception as e:
            print(f"保存PCM失败: {e}")
            return False
    
    def extract_features(self, audio_data):
        """提取音频特征"""
        num_frames = len(audio_data) // self.frame_size
        if num_frames < 1:
            return None
        
        features = []
        
        for i in range(num_frames):
            start_idx = i * self.frame_size
            end_idx = start_idx + self.frame_size
            
            if end_idx > len(audio_data):
                # 填充不足的帧
                frame = np.zeros(self.frame_size, dtype=np.float32)
                frame[:len(audio_data) - start_idx] = audio_data[start_idx:]
            else:
                frame = audio_data[start_idx:end_idx]
            
            # 计算特征 (65维)
            # 1. 能量特征 (32维)
            energy_features = np.zeros(32)
            for j in range(32):
                band_start = j * self.frame_size // 32
                band_end = (j + 1) * self.frame_size // 32
                energy_features[j] = np.mean(frame[band_start:band_end] ** 2)
            
            # 2. 频谱特征 (32维)
            fft_frame = np.fft.fft(frame)
            fft_magnitude = np.abs(fft_frame[:self.frame_size//2])
            spectral_features = np.zeros(32)
            for j in range(32):
                band_start = j * len(fft_magnitude) // 32
                band_end = (j + 1) * len(fft_magnitude) // 32
                spectral_features[j] = np.mean(fft_magnitude[band_start:band_end])
            
            # 3. 基音特征 (1维)
            pitch_feature = np.array([np.std(frame)])
            
            # 组合特征 (65维)
            frame_features = np.concatenate([
                energy_features,      # 32维
                spectral_features,    # 32维
                pitch_feature         # 1维
            ])
            
            features.append(frame_features)
        
        return np.array(features, dtype=np.float32)

class RNNoiseDenoiser:
    """RNNoise降噪器"""
    
    def __init__(self, model_path, device='cpu'):
        self.device = torch.device(device)
        self.model = self.load_model(model_path)
        
    def load_model(self, model_path):
        """加载训练好的模型"""
        print(f"加载模型: {model_path}")
        
        try:
            # 加载检查点
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # 获取模型参数
            model_kwargs = checkpoint.get('model_kwargs', {'cond_size': 128, 'gru_size': 384})
            
            # 创建模型
            model = rnnoise.RNNoise(**model_kwargs)
            model.load_state_dict(checkpoint['state_dict'])
            model.to(self.device)
            model.eval()
            
            print(f"模型加载成功，参数数量: {sum(p.numel() for p in model.parameters())}")
            return model
            
        except Exception as e:
            print(f"模型加载失败: {e}")
            return None
    
    def denoise_features(self, features):
        """对特征进行降噪"""
        if self.model is None:
            return None
        
        try:
            # 转换为tensor
            features_tensor = torch.from_numpy(features).unsqueeze(0).to(self.device)  # (1, frames, 65)
            
            with torch.no_grad():
                # 模型推理
                pred_gain, pred_vad, _ = self.model(features_tensor)
                
                # 获取预测的增益
                gain = pred_gain.squeeze(0).cpu().numpy()  # (frames-4, 32)
                
            return gain
            
        except Exception as e:
            print(f"降噪处理失败: {e}")
            return None
    
    def apply_gain(self, audio_data, gain, frame_size=480):
        """将增益应用到音频数据"""
        if gain is None:
            return audio_data
        
        # 计算有效帧数
        num_frames = min(len(audio_data) // frame_size, gain.shape[0] + 4)
        
        # 创建输出音频
        output_audio = np.copy(audio_data)
        
        for i in range(3, num_frames - 1):  # 跳过前3帧和最后1帧
            gain_idx = i - 3
            if gain_idx >= gain.shape[0]:
                break
                
            start_idx = i * frame_size
            end_idx = start_idx + frame_size
            
            if end_idx > len(output_audio):
                break
            
            # 应用频带增益
            frame = output_audio[start_idx:end_idx]
            
            for j in range(32):
                band_start = j * frame_size // 32
                band_end = (j + 1) * frame_size // 32
                
                # 应用增益
                band_gain = np.clip(gain[gain_idx, j], 0, 1)
                frame[band_start:band_end] *= band_gain
            
            output_audio[start_idx:end_idx] = frame
        
        return output_audio

def main():
    parser = argparse.ArgumentParser(description='RNNoise音频降噪测试')
    parser.add_argument('input_wav', nargs='?', default=DEFAULT_INPUT_WAV,
                       help=f'输入WAV文件 (默认: {DEFAULT_INPUT_WAV})')
    parser.add_argument('output_wav', nargs='?', default=DEFAULT_OUTPUT_WAV,
                       help=f'输出WAV文件 (默认: {DEFAULT_OUTPUT_WAV})')
    parser.add_argument('--model', default='RNNoise_train\model_output\checkpoints\rnnoise_best.pth',
                       help='模型文件路径')
    parser.add_argument('--device', default='cpu', choices=['cpu', 'cuda'],
                       help='计算设备')

    args = parser.parse_args()
    
    print("=== RNNoise音频降噪测试 ===")
    print(f"输入文件: {args.input_wav}")
    print(f"输出文件: {args.output_wav}")
    print(f"模型文件: {args.model}")
    print(f"计算设备: {args.device}")
    
    # 检查输入文件
    if not os.path.exists(args.input_wav):
        print(f"错误: 输入文件不存在: {args.input_wav}")
        return
    
    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"错误: 模型文件不存在: {args.model}")
        return
    
    # 创建处理器
    processor = AudioProcessor()
    denoiser = RNNoiseDenoiser(args.model, args.device)
    
    if denoiser.model is None:
        print("模型加载失败，退出")
        return
    
    # 创建临时文件
    with tempfile.TemporaryDirectory() as temp_dir:
        input_pcm = os.path.join(temp_dir, "input.pcm")
        output_pcm = os.path.join(temp_dir, "output.pcm")
        
        print("\n步骤1: WAV转PCM")
        if not processor.wav_to_pcm(args.input_wav, input_pcm):
            return
        
        print("步骤2: 加载音频数据")
        audio_data = processor.load_pcm(input_pcm)
        if audio_data is None:
            return
        
        print(f"音频长度: {len(audio_data)} 样本 ({len(audio_data)/48000:.2f}秒)")
        
        print("步骤3: 提取特征")
        features = processor.extract_features(audio_data)
        if features is None:
            print("特征提取失败")
            return
        
        print(f"特征形状: {features.shape}")
        
        print("步骤4: 模型降噪")
        gain = denoiser.denoise_features(features)
        if gain is None:
            print("降噪处理失败")
            return
        
        print(f"增益形状: {gain.shape}")
        
        print("步骤5: 应用增益")
        denoised_audio = denoiser.apply_gain(audio_data, gain)
        
        print("步骤6: 保存降噪音频")
        if not processor.save_pcm(denoised_audio, output_pcm):
            return
        
        print("步骤7: PCM转WAV")
        if not processor.pcm_to_wav(output_pcm, args.output_wav):
            return
    
    print(f"\n✓ 降噪完成！")
    print(f"输出文件: {args.output_wav}")
    
    # 显示文件信息
    input_size = os.path.getsize(args.input_wav) / 1024
    output_size = os.path.getsize(args.output_wav) / 1024
    print(f"输入文件大小: {input_size:.1f} KB")
    print(f"输出文件大小: {output_size:.1f} KB")

if __name__ == "__main__":
    main()
