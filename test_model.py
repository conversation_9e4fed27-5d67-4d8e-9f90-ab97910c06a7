#!/usr/bin/env python3
"""
测试训练好的RNNoise模型
验证模型可以正常加载和推理
"""

import os
import sys
import torch
import numpy as np
import argparse

# 添加torch/rnnoise到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'torch', 'rnnoise'))
import rnnoise

def load_model(model_path):
    """加载训练好的模型"""
    print(f"加载模型: {model_path}")
    
    # 检查文件是否存在
    if not os.path.exists(model_path):
        print(f"错误: 模型文件不存在: {model_path}")
        return None
    
    try:
        # 加载检查点
        checkpoint = torch.load(model_path, map_location='cpu')
        
        # 打印模型信息
        print(f"模型信息:")
        print(f"  轮次: {checkpoint.get('epoch', 'N/A')}")
        print(f"  损失: {checkpoint.get('loss', 'N/A'):.5f}")
        print(f"  模型参数: {checkpoint.get('model_kwargs', {})}")
        
        # 创建模型
        model_kwargs = checkpoint.get('model_kwargs', {'cond_size': 128, 'gru_size': 256})
        model = rnnoise.RNNoise(**model_kwargs)
        
        # 加载权重
        model.load_state_dict(checkpoint['state_dict'])
        model.eval()
        
        print("模型加载成功！")
        return model
        
    except Exception as e:
        print(f"加载模型失败: {e}")
        return None

def test_model_inference(model, batch_size=1, sequence_length=100):
    """测试模型推理"""
    print(f"\n测试模型推理...")
    print(f"批次大小: {batch_size}, 序列长度: {sequence_length}")
    
    # 创建随机输入数据 (batch_size, sequence_length, 65)
    input_features = torch.randn(batch_size, sequence_length, 65)
    
    try:
        with torch.no_grad():
            # 前向传播
            pred_gain, pred_vad, states = model(input_features)
            
            print(f"输入形状: {input_features.shape}")
            print(f"预测增益形状: {pred_gain.shape}")
            print(f"预测VAD形状: {pred_vad.shape}")
            print(f"状态数量: {len(states)}")
            
            # 检查输出范围
            gain_min, gain_max = pred_gain.min().item(), pred_gain.max().item()
            vad_min, vad_max = pred_vad.min().item(), pred_vad.max().item()
            
            print(f"增益范围: [{gain_min:.4f}, {gain_max:.4f}]")
            print(f"VAD范围: [{vad_min:.4f}, {vad_max:.4f}]")
            
            print("推理测试成功！")
            return True
            
    except Exception as e:
        print(f"推理测试失败: {e}")
        return False

def compare_models(model_dir):
    """比较不同轮次的模型"""
    print(f"\n比较模型性能...")
    
    model_files = [f for f in os.listdir(model_dir) if f.endswith('.pth')]
    model_files.sort()
    
    results = []
    
    for model_file in model_files:
        model_path = os.path.join(model_dir, model_file)
        
        try:
            checkpoint = torch.load(model_path, map_location='cpu')
            epoch = checkpoint.get('epoch', 0)
            loss = checkpoint.get('loss', float('inf'))
            
            results.append((model_file, epoch, loss))
            
        except Exception as e:
            print(f"无法读取 {model_file}: {e}")
    
    # 按损失排序
    results.sort(key=lambda x: x[2])
    
    print("模型性能排序 (按损失从低到高):")
    print("文件名\t\t\t轮次\t损失")
    print("-" * 50)
    
    for model_file, epoch, loss in results:
        print(f"{model_file:<25}\t{epoch}\t{loss:.5f}")
    
    if results:
        best_model = results[0]
        print(f"\n最佳模型: {best_model[0]} (轮次 {best_model[1]}, 损失 {best_model[2]:.5f})")

def main():
    parser = argparse.ArgumentParser(description='测试训练好的RNNoise模型')
    parser.add_argument('--model-path', default='model/best_model.pth',
                       help='模型文件路径')
    parser.add_argument('--model-dir', default='model',
                       help='模型目录（用于比较）')
    parser.add_argument('--batch-size', type=int, default=2,
                       help='测试批次大小')
    parser.add_argument('--sequence-length', type=int, default=100,
                       help='测试序列长度')
    parser.add_argument('--compare', action='store_true',
                       help='比较所有模型')
    
    args = parser.parse_args()
    
    print("=== RNNoise模型测试 ===")
    
    # 测试单个模型
    if os.path.exists(args.model_path):
        model = load_model(args.model_path)
        
        if model is not None:
            # 测试推理
            success = test_model_inference(model, args.batch_size, args.sequence_length)
            
            if success:
                print("\n✓ 模型测试通过！")
            else:
                print("\n✗ 模型测试失败！")
    else:
        print(f"模型文件不存在: {args.model_path}")
    
    # 比较所有模型
    if args.compare and os.path.exists(args.model_dir):
        compare_models(args.model_dir)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
