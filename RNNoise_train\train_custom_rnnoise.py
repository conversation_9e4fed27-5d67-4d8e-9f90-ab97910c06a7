#!/usr/bin/env python3
"""
自定义RNNoise训练脚本
将WAV文件转换为PCM，提取特征，训练模型并保存权重
"""

import os
import sys
import numpy as np
import subprocess
import argparse
import glob
from pathlib import Path
import struct
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import tqdm

# 添加torch/rnnoise到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'torch', 'rnnoise'))
import rnnoise

class AudioProcessor:
    """音频处理类，负责WAV到PCM的转换"""
    
    def __init__(self, ffmpeg_path="env/ffmpeg.exe"):
        self.ffmpeg_path = ffmpeg_path
        
    def wav_to_pcm(self, wav_file, pcm_file, sample_rate=48000):
        """将WAV文件转换为16位PCM格式"""
        cmd = [
            self.ffmpeg_path,
            '-i', wav_file,
            '-f', 's16le',  # 16位小端格式
            '-ar', str(sample_rate),  # 采样率
            '-ac', '1',  # 单声道
            '-y',  # 覆盖输出文件
            pcm_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"转换失败 {wav_file}: {e.stderr}")
            return False
    
    def process_directory(self, input_dir, output_dir, sample_rate=48000):
        """批量处理目录中的WAV文件"""
        os.makedirs(output_dir, exist_ok=True)
        
        wav_files = glob.glob(os.path.join(input_dir, "*.wav"))
        print(f"找到 {len(wav_files)} 个WAV文件在 {input_dir}")
        
        success_count = 0
        for wav_file in tqdm.tqdm(wav_files, desc=f"转换 {input_dir}"):
            filename = os.path.splitext(os.path.basename(wav_file))[0]
            pcm_file = os.path.join(output_dir, f"{filename}.pcm")
            
            if self.wav_to_pcm(wav_file, pcm_file, sample_rate):
                success_count += 1
        
        print(f"成功转换 {success_count}/{len(wav_files)} 个文件")
        return success_count

class FeatureExtractor:
    """特征提取类"""
    
    def __init__(self, dump_features_exe="src/dump_features"):
        self.dump_features_exe = dump_features_exe
        
    def extract_features(self, clean_pcm_dir, noise_pcm_dir, output_file):
        """提取特征并保存为.f32格式"""
        
        # 获取PCM文件列表
        clean_files = sorted(glob.glob(os.path.join(clean_pcm_dir, "*.pcm")))
        noise_files = sorted(glob.glob(os.path.join(noise_pcm_dir, "*.pcm")))
        
        print(f"清晰语音文件: {len(clean_files)}")
        print(f"噪声语音文件: {len(noise_files)}")
        
        if len(clean_files) == 0 or len(noise_files) == 0:
            raise ValueError("没有找到PCM文件")
        
        # 创建输出目录
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 使用dump_features提取特征
        # 这里我们需要编译dump_features程序
        if not os.path.exists(self.dump_features_exe):
            print("编译dump_features程序...")
            self._compile_dump_features()
        
        # 创建临时文件列表
        clean_list_file = "temp_clean_list.txt"
        noise_list_file = "temp_noise_list.txt"
        
        with open(clean_list_file, 'w') as f:
            for file in clean_files:
                f.write(f"{file}\n")
                
        with open(noise_list_file, 'w') as f:
            for file in noise_files:
                f.write(f"{file}\n")
        
        # 运行特征提取
        cmd = [
            self.dump_features_exe,
            clean_list_file,
            noise_list_file,
            output_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("特征提取完成")
            
            # 清理临时文件
            os.remove(clean_list_file)
            os.remove(noise_list_file)
            
            return True
        except subprocess.CalledProcessError as e:
            print(f"特征提取失败: {e.stderr}")
            return False
    
    def _compile_dump_features(self):
        """编译dump_features程序"""
        # 这里需要根据实际的编译环境调整
        print("请先编译dump_features程序")
        print("在src目录下运行: gcc -o dump_features dump_features.c ...")
        raise NotImplementedError("需要先编译dump_features程序")

class RNNoiseDataset(Dataset):
    """RNNoise数据集类"""
    
    def __init__(self, features_file, sequence_length=2000):
        self.sequence_length = sequence_length
        
        # 加载特征数据
        self.data = np.memmap(features_file, dtype='float32', mode='r')
        dim = 98  # NB_FEATURES(65) + NB_BANDS(32) + VAD(1) = 98
        
        self.nb_sequences = self.data.shape[0] // self.sequence_length // dim
        self.data = self.data[:self.nb_sequences * self.sequence_length * dim]
        
        self.data = np.reshape(self.data, (self.nb_sequences, self.sequence_length, dim))
        
        print(f"数据集大小: {self.nb_sequences} 个序列")
    
    def __len__(self):
        return self.nb_sequences
    
    def __getitem__(self, index):
        # 返回特征(65维)、增益(32维)、VAD(1维)
        return (
            self.data[index, :, :65].copy(),  # 特征
            self.data[index, :, 65:-1].copy(),  # 增益
            self.data[index, :, -1:].copy()   # VAD
        )

def train_model(features_file, model_dir, epochs=200, batch_size=128, lr=1e-3):
    """训练RNNoise模型"""
    
    # 创建模型目录
    os.makedirs(model_dir, exist_ok=True)
    
    # 设备选择
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 创建数据集和数据加载器
    dataset = RNNoiseDataset(features_file)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True, 
                          drop_last=True, num_workers=4)
    
    # 创建模型
    model = rnnoise.RNNoise(cond_size=128, gru_size=384)
    model.to(device)
    
    # 优化器和学习率调度器
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr, 
                                betas=[0.8, 0.98], eps=1e-8)
    scheduler = torch.optim.lr_scheduler.LambdaLR(
        optimizer=optimizer, 
        lr_lambda=lambda x: 1 / (1 + 5e-5 * x)
    )
    
    # 训练循环
    best_loss = float('inf')
    
    for epoch in range(1, epochs + 1):
        model.train()
        running_loss = 0.0
        running_gain_loss = 0.0
        running_vad_loss = 0.0
        
        print(f"训练轮次 {epoch}/{epochs}")
        
        with tqdm.tqdm(dataloader, unit='batch') as tepoch:
            for i, (features, gain, vad) in enumerate(tepoch):
                optimizer.zero_grad()
                
                # 数据移到设备
                features = features.to(device)
                gain = gain.to(device)
                vad = vad.to(device)
                
                # 前向传播
                pred_gain, pred_vad, states = model(features)
                
                # 计算损失
                gain = gain[:, 3:-1, :]  # 去掉前3帧和最后1帧
                vad = vad[:, 3:-1, :]
                
                target_gain = torch.clamp(gain, min=0)
                target_gain = target_gain * (torch.tanh(8 * target_gain) ** 2)
                
                # 增益损失
                gamma = 0.25
                e = pred_gain ** gamma - target_gain ** gamma
                mask_val = torch.clamp(gain + 1, max=1)
                gain_loss = torch.mean((1 + 5. * vad) * mask_val * (e ** 2))
                
                # VAD损失
                vad_loss = torch.mean(
                    torch.abs(2 * vad - 1) * (
                        -vad * torch.log(0.01 + pred_vad) - 
                        (1 - vad) * torch.log(1.01 - pred_vad)
                    )
                )
                
                total_loss = gain_loss + 0.001 * vad_loss
                
                # 反向传播
                total_loss.backward()
                optimizer.step()
                scheduler.step()
                
                # 统计
                running_loss += total_loss.item()
                running_gain_loss += gain_loss.item()
                running_vad_loss += vad_loss.item()
                
                tepoch.set_postfix(
                    loss=f"{running_loss/(i+1):.5f}",
                    gain_loss=f"{running_gain_loss/(i+1):.5f}",
                    vad_loss=f"{running_vad_loss/(i+1):.5f}"
                )
        
        # 保存检查点
        avg_loss = running_loss / len(dataloader)
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'loss': avg_loss,
            'model_args': (),
            'model_kwargs': {'cond_size': 128, 'gru_size': 384}
        }
        
        checkpoint_path = os.path.join(model_dir, f'rnnoise_epoch_{epoch}.pth')
        torch.save(checkpoint, checkpoint_path)
        
        # 保存最佳模型
        if avg_loss < best_loss:
            best_loss = avg_loss
            best_model_path = os.path.join(model_dir, 'best_model.pth')
            torch.save(checkpoint, best_model_path)
            print(f"保存最佳模型，损失: {best_loss:.5f}")
    
    print("训练完成！")
    return model

def main():
    parser = argparse.ArgumentParser(description='训练自定义RNNoise模型')
    parser.add_argument('--clean-dir', default='clean_voice', 
                       help='清晰语音WAV文件目录')
    parser.add_argument('--noise-dir', default='noise_voice', 
                       help='噪声语音WAV文件目录')
    parser.add_argument('--model-dir', default='model', 
                       help='模型保存目录')
    parser.add_argument('--epochs', type=int, default=200, 
                       help='训练轮次')
    parser.add_argument('--batch-size', type=int, default=128, 
                       help='批次大小')
    parser.add_argument('--lr', type=float, default=1e-3, 
                       help='学习率')
    parser.add_argument('--sample-rate', type=int, default=48000, 
                       help='采样率')
    
    args = parser.parse_args()
    
    print("=== 自定义RNNoise训练流程 ===")
    
    # 步骤1: WAV转PCM
    print("\n步骤1: 转换WAV文件为PCM格式")
    processor = AudioProcessor()
    
    clean_pcm_dir = "temp_clean_pcm"
    noise_pcm_dir = "temp_noise_pcm"
    
    processor.process_directory(args.clean_dir, clean_pcm_dir, args.sample_rate)
    processor.process_directory(args.noise_dir, noise_pcm_dir, args.sample_rate)
    
    # 步骤2: 特征提取
    print("\n步骤2: 提取特征")
    extractor = FeatureExtractor()
    features_file = "features/training_features.f32"
    
    try:
        extractor.extract_features(clean_pcm_dir, noise_pcm_dir, features_file)
    except NotImplementedError:
        print("请先编译特征提取程序，然后重新运行")
        return
    
    # 步骤3: 训练模型
    print("\n步骤3: 训练模型")
    model = train_model(features_file, args.model_dir, 
                       args.epochs, args.batch_size, args.lr)
    
    print(f"\n训练完成！模型已保存到 {args.model_dir}")
    print("最佳模型: best_model.pth")

if __name__ == "__main__":
    main()
