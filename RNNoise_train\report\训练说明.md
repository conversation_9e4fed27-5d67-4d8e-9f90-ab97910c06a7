# RNNoise 训练说明

## 📁 文件结构

```
RNNoise_train/
├── clean_PCM_data/          # 443个清晰语音PCM文件
├── noise_PCM_data/          # 443个噪声语音PCM文件
├── features/                # 特征文件
│   ├── clean_features.f32   # 清晰语音特征
│   ├── noise_features.f32   # 噪声语音特征
│   └── training_features.f32 # 混合训练特征 ⭐ 推荐使用
├── model_output/            # 训练输出目录
├── prepare_pcm_data.py      # WAV转PCM脚本
├── extract_features_python.py # 混合特征提取
├── extract_separate_features.py # 分离特征提取
└── train.py                 # 训练脚本 ⭐ 唯一训练代码
```

## 🎯 推荐使用的特征文件

**使用 `features/training_features.f32`**

这个文件包含了清晰语音和噪声语音的配对特征，最适合RNNoise降噪训练。

## 🚀 训练方法

### 基础训练 (200轮)
```bash
cd RNNoise_train
python train.py
```

### 增加训练轮次 (500轮)
```bash
python train.py --epochs 500
```

### 高级训练 (1000轮 + 自定义参数)
```bash
python train.py --epochs 1000 --batch-size 32 --lr 0.001
```

### 快速测试 (2轮)
```bash
python train.py --epochs 2 --batch-size 16
```

## ⚙️ 训练参数说明

| 参数 | 默认值 | 说明 | 建议值 |
|------|--------|------|--------|
| `--epochs` | 200 | 训练轮次 | 500-1000 |
| `--batch-size` | 32 | 批次大小 | 16-64 |
| `--lr` | 1e-3 | 学习率 | 1e-3 |
| `--sequence-length` | 200 | 序列长度 | 200 (固定) |

## 📊 训练输出

训练完成后，模型文件保存在 `model_output/checkpoints/` 目录：

```
model_output/
└── checkpoints/
    ├── rnnoise_1.pth
    ├── rnnoise_2.pth
    ├── ...
    └── rnnoise_500.pth
```

## 🔧 如何增加训练轮次

### 方法1: 直接指定轮次
```bash
python train.py --epochs 1000
```

### 方法2: 修改默认值
编辑 `train.py` 文件，将第15行改为：
```python
parser.add_argument('--epochs', type=int, default=1000,
```

## 💡 训练建议

### 快速测试 (50轮)
```bash
python train.py --epochs 50 --batch-size 32
```

### 标准训练 (500轮)
```bash
python train.py --epochs 500 --batch-size 128
```

### 长时间训练 (1000轮)
```bash
python train.py --epochs 1000 --batch-size 64
```

## 📈 监控训练

训练过程中会显示：
- 当前轮次
- 训练损失
- 增益损失
- VAD损失

训练完成后会显示生成的模型文件列表。

## ⚠️ 注意事项

1. **特征文件位置**: 必须使用 `features/training_features.f32`
2. **序列长度**: 必须设置为200 (匹配特征文件格式)
3. **内存需求**: 批次大小越大，内存需求越高
4. **训练时间**: 轮次越多，训练时间越长

## 🎉 训练完成后

1. **查看模型**: 检查 `model_output/checkpoints/` 中的.pth文件
2. **选择最佳模型**: 通常最后几轮的模型效果最好
3. **转换为C代码** (可选):
   ```bash
   python ../torch/rnnoise/dump_rnnoise_weights.py --quantize model_output/checkpoints/rnnoise_500.pth output_c
   ```

---

**简单总结**: 使用 `python train.py --epochs 500` 开始训练，特征文件自动使用 `features/training_features.f32`
