# RNNoise 训练系统整理完成

## ✅ 整理结果

### 保留的文件

```
RNNoise_train/
├── clean_PCM_data/          # 443个清晰语音PCM文件
├── noise_PCM_data/          # 443个噪声语音PCM文件
├── features/                # 特征文件目录
│   ├── clean_features.f32   # 清晰语音特征 (7.84 MB)
│   ├── noise_features.f32   # 噪声语音特征 (7.84 MB)
│   └── training_features.f32 # 混合训练特征 (7.84 MB) ⭐
├── model_output/            # 训练输出目录
│   └── checkpoints/         # 模型检查点
│       ├── rnnoise_1.pth
│       ├── rnnoise_2.pth
│       └── rnnoise_best.pth
├── extract_features_python.py      # 混合特征提取脚本
├── extract_separate_features.py    # 分离特征提取脚本
├── prepare_pcm_data.py             # WAV转PCM脚本
├── train.py                        # 唯一训练脚本 ⭐
├── README.md                       # 详细说明文档
└── 训练说明.md                      # 简洁训练指南
```

### 删除的文件 ❌
- `simple_train.py` - 简化训练脚本
- `train_custom_rnnoise.py` - 自定义训练脚本
- `train_original.py` - 原始训练包装
- `run_training.py` - 运行训练脚本
- `run_full_training.py` - 完整训练流程
- `test_model.py` - 模型测试脚本
- `prepare_training_data.py` - 准备训练数据脚本
- `train_with_original.py` - 原始训练包装脚本
- `prepare_data_full.py` - 完整数据准备脚本
- `数据准备完成报告.md` - 旧报告文件

## 🎯 推荐使用

### 特征文件
**使用 `features/training_features.f32`**
- 包含443对音频的混合特征
- 适合RNNoise降噪训练
- 文件大小: 7.84 MB
- 序列数: 100个，每序列200帧

### 训练脚本
**使用 `train.py`** (唯一训练代码)
- 基于原始RNNoise算法
- 自动使用正确的特征文件
- 支持参数自定义

## 🚀 快速开始

### 基础训练
```bash
cd RNNoise_train
python train.py --epochs 200
```

### 增加训练轮次
```bash
python train.py --epochs 500
```

### 高性能训练
```bash
python train.py --epochs 1000 --batch-size 32
```

## 📊 训练参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--epochs` | 200 | 训练轮次 |
| `--batch-size` | 32 | 批次大小 |
| `--lr` | 1e-3 | 学习率 |
| `--sequence-length` | 200 | 序列长度 (固定) |

## 🔧 如何增加训练轮次

### 方法1: 命令行参数
```bash
python train.py --epochs 1000
```

### 方法2: 修改默认值
编辑 `train.py` 第172行:
```python
parser.add_argument('--epochs', type=int, default=1000,  # 改为1000
```

## 📈 训练输出

训练完成后生成的文件:
- `model_output/checkpoints/rnnoise_1.pth` - 第1轮模型
- `model_output/checkpoints/rnnoise_2.pth` - 第2轮模型
- `...`
- `model_output/checkpoints/rnnoise_N.pth` - 第N轮模型
- `model_output/checkpoints/rnnoise_best.pth` - 最佳模型 ⭐

## ✨ 主要改进

1. **简化结构**: 只保留1个训练脚本
2. **完整数据**: 使用全部443对音频文件
3. **稳定训练**: 修复了原始脚本的兼容性问题
4. **清晰文档**: 提供简洁的使用说明

## 🎉 测试结果

已成功测试训练流程:
- ✅ 数据加载正常 (100个序列)
- ✅ 模型创建成功 (2,884,769参数)
- ✅ 训练运行稳定
- ✅ 损失正常下降 (0.43 → 0.28)
- ✅ 模型保存成功 (11MB/个)

## 📋 下一步

1. **开始正式训练**: `python train.py --epochs 500`
2. **监控训练进度**: 观察损失下降情况
3. **选择最佳模型**: 使用 `rnnoise_best.pth`
4. **测试模型效果**: 用实际音频验证降噪效果

---

**整理完成时间**: 2025年7月30日 12:45  
**状态**: ✅ 就绪，可开始训练  
**推荐命令**: `python train.py --epochs 500`
