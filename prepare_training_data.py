#!/usr/bin/env python3
"""
准备RNNoise训练数据
1. 将WAV文件转换为PCM
2. 使用dump_features提取特征
3. 准备训练数据
"""

import os
import sys
import subprocess
import glob
import argparse
import numpy as np
import tqdm
from pathlib import Path

class DataPreparator:
    """数据准备类"""
    
    def __init__(self, ffmpeg_path="env/ffmpeg.exe"):
        self.ffmpeg_path = ffmpeg_path
        
    def wav_to_pcm(self, wav_file, pcm_file, sample_rate=48000):
        """将WAV文件转换为16位PCM格式"""
        cmd = [
            self.ffmpeg_path,
            '-i', wav_file,
            '-f', 's16le',  # 16位小端格式
            '-ar', str(sample_rate),  # 采样率
            '-ac', '1',  # 单声道
            '-y',  # 覆盖输出文件
            pcm_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"转换失败 {wav_file}: {e.stderr}")
            return False
    
    def process_wav_directory(self, input_dir, output_dir, sample_rate=48000, max_files=None):
        """批量处理目录中的WAV文件"""
        os.makedirs(output_dir, exist_ok=True)
        
        wav_files = sorted(glob.glob(os.path.join(input_dir, "*.wav")))
        if max_files:
            wav_files = wav_files[:max_files]
            
        print(f"找到 {len(wav_files)} 个WAV文件在 {input_dir}")
        
        success_files = []
        for wav_file in tqdm.tqdm(wav_files, desc=f"转换 {os.path.basename(input_dir)}"):
            filename = os.path.splitext(os.path.basename(wav_file))[0]
            pcm_file = os.path.join(output_dir, f"{filename}.pcm")
            
            if self.wav_to_pcm(wav_file, pcm_file, sample_rate):
                success_files.append(pcm_file)
        
        print(f"成功转换 {len(success_files)}/{len(wav_files)} 个文件")
        return success_files
    
    def create_file_list(self, pcm_files, list_file):
        """创建文件列表"""
        with open(list_file, 'w') as f:
            for pcm_file in pcm_files:
                f.write(f"{os.path.abspath(pcm_file)}\n")
        return list_file
    
    def extract_features_with_dump(self, clean_files, noise_files, output_file, count=1000):
        """使用dump_features程序提取特征"""
        
        # 检查dump_features是否存在
        dump_features_exe = "src/dump_features.exe"
        if not os.path.exists(dump_features_exe):
            dump_features_exe = "src/dump_features"
            if not os.path.exists(dump_features_exe):
                print("错误: 找不到dump_features程序")
                print("请先编译dump_features:")
                print("cd src && gcc -o dump_features dump_features.c denoise.c kiss_fft.c celt_lpc.c pitch.c nnet.c rnn.c parse_lpcnet_weights.c -lm")
                return False
        
        # 创建临时文件列表
        clean_list = "temp_clean_list.txt"
        noise_list = "temp_noise_list.txt"
        
        # 确保有足够的文件
        min_files = min(len(clean_files), len(noise_files))
        if min_files == 0:
            print("错误: 没有找到PCM文件")
            return False
        
        # 创建文件列表
        self.create_file_list(clean_files[:min_files], clean_list)
        self.create_file_list(noise_files[:min_files], noise_list)
        
        # 创建输出目录
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 运行dump_features
        cmd = [
            dump_features_exe,
            clean_list,
            noise_list,
            "temp_empty.txt",  # 前景噪声文件列表（空）
            output_file,
            str(count)
        ]
        
        print(f"运行特征提取: {' '.join(cmd)}")
        
        try:
            # 创建空的前景噪声文件列表
            with open("temp_empty.txt", 'w') as f:
                f.write("")
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("特征提取完成")
            print(f"输出: {result.stdout}")
            
            # 清理临时文件
            for temp_file in [clean_list, noise_list, "temp_empty.txt"]:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"特征提取失败: {e.stderr}")
            print(f"输出: {e.stdout}")
            return False
    
    def prepare_simple_features(self, clean_files, noise_files, output_file, frames_per_file=1000):
        """创建简化的特征文件用于测试"""
        print("创建简化特征文件用于测试...")
        
        # 创建输出目录
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 生成随机特征数据
        num_files = min(len(clean_files), len(noise_files), 10)  # 限制文件数量
        total_frames = num_files * frames_per_file
        
        # 特征维度: 65(features) + 32(gains) + 1(vad) = 98
        features = np.random.randn(total_frames, 98).astype(np.float32)
        
        # 保存为.f32格式
        features.tofile(output_file)
        
        print(f"创建了 {total_frames} 帧特征数据")
        print(f"特征文件保存到: {output_file}")
        
        return True

def main():
    parser = argparse.ArgumentParser(description='准备RNNoise训练数据')
    parser.add_argument('--clean-dir', default='clean_voice', 
                       help='清晰语音WAV文件目录')
    parser.add_argument('--noise-dir', default='noise_voice', 
                       help='噪声语音WAV文件目录')
    parser.add_argument('--output-dir', default='features', 
                       help='特征输出目录')
    parser.add_argument('--sample-rate', type=int, default=48000, 
                       help='采样率')
    parser.add_argument('--max-files', type=int, default=50, 
                       help='最大处理文件数')
    parser.add_argument('--count', type=int, default=500, 
                       help='生成的训练序列数量')
    parser.add_argument('--simple', action='store_true',
                       help='使用简化特征提取（用于测试）')
    
    args = parser.parse_args()
    
    print("=== RNNoise数据准备流程 ===")
    
    preparator = DataPreparator()
    
    # 步骤1: 转换WAV到PCM
    print("\n步骤1: 转换WAV文件为PCM格式")
    
    clean_pcm_dir = "temp_clean_pcm"
    noise_pcm_dir = "temp_noise_pcm"
    
    clean_files = preparator.process_wav_directory(
        args.clean_dir, clean_pcm_dir, args.sample_rate, args.max_files)
    noise_files = preparator.process_wav_directory(
        args.noise_dir, noise_pcm_dir, args.sample_rate, args.max_files)
    
    if not clean_files or not noise_files:
        print("错误: 没有成功转换任何文件")
        return
    
    # 步骤2: 提取特征
    print("\n步骤2: 提取特征")
    
    output_file = os.path.join(args.output_dir, "training_features.f32")
    
    if args.simple:
        # 使用简化特征提取
        success = preparator.prepare_simple_features(clean_files, noise_files, output_file)
    else:
        # 使用dump_features程序
        success = preparator.extract_features_with_dump(
            clean_files, noise_files, output_file, args.count)
    
    if success:
        print(f"\n数据准备完成！")
        print(f"特征文件: {output_file}")
        print(f"文件大小: {os.path.getsize(output_file) / (1024*1024):.2f} MB")
        
        # 验证特征文件
        try:
            data = np.memmap(output_file, dtype='float32', mode='r')
            print(f"特征数据形状: {data.shape}")
            print(f"预计序列数: {data.shape[0] // (2000 * 98)}")
        except Exception as e:
            print(f"验证特征文件时出错: {e}")
    else:
        print("数据准备失败！")

if __name__ == "__main__":
    main()
